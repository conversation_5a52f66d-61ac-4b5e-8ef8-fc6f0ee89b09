package com.inossem.wms.bizdomain.bi.controller;

import com.inossem.wms.bizbasis.bi.restful.service.HXSapBiInterfaceService;
import com.inossem.wms.bizdomain.bi.service.biz.BiPurchaseService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractSumVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.erp.po.ErpBiSearchPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * BI-华信资源驾驶舱-采购类指标 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@Api(tags = "BI-华信资源驾驶舱-采购类指标")
public class BiPurchaseController {

    @Autowired
    protected BiPurchaseService biPurchaseService;
    @Autowired
    private HXSapBiInterfaceService hXSapBiInterfaceService;

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同数量/金额-总和", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-sum/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BiCompletedContractSumVO> getCompletedContractSum(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractSum(ctx);
        BiCompletedContractSumVO vo = ctx.getVoContextData();
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同数量", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-count/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiCompletedContractCountVO>> getCompletedContractCount(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractCount(ctx);
        List<BiCompletedContractCountVO> voList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(voList);
    }

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同金额", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-amount/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiCompletedContractAmountVO>> getCompletedContractAmount(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractAmount(ctx);
        List<BiCompletedContractAmountVO> voList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(voList);
    }

    @ApiOperation(value = "BI-采购类指标-test", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @GetMapping(path = "/bi-purchase/test", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> test(@RequestBody BiSearchPO po, BizContext ctx) {
        hXSapBiInterfaceService.synBiBsegInfo(new ErpBiSearchPO());
        return BaseResult.success();
    }

}

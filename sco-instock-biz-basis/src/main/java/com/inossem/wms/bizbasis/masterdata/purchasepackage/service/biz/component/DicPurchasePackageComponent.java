package com.inossem.wms.bizbasis.masterdata.purchasepackage.service.biz.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.masterdata.purchasepackage.service.datawrap.DicPurchasePackageDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.purchasepackage.dto.DicPurchasePackageDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSavePO;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSearchPO;
import com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName DicPurchasePackageComponent
 * @description: 采购包主数据
 * @date 2024/07/16 09:36
 **/
@Slf4j
@Component
public class DicPurchasePackageComponent {

    @Autowired
    protected DicPurchasePackageDataWrap dicPurchasePackageDataWrap;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private EditCacheService editCacheService;

    public PageObjectVO<DicPurchasePackagePageVO> getPage(BizContext ctx) {
        DicPurchasePackageSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("采购包主数据列表 po：{}", JSONObject.toJSONString(po));
        if (UtilObject.isNull(po)) {
            po = new DicPurchasePackageSearchPO();
        }
        IPage<DicPurchasePackagePageVO> page = po.getPageObj(DicPurchasePackagePageVO.class);
        dicPurchasePackageDataWrap.getPageVoList(page, po);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    public DicPurchasePackageDTO get(Long id) {
        log.info("采购包管理详情查询 id：{}", id);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicPurchasePackage entity = dicPurchasePackageDataWrap.getById(id);
        log.info("采购包管理id：{}，详情：{}", id, JSONObject.toJSONString(entity));
        DicPurchasePackageDTO dto = UtilBean.newInstance(entity, DicPurchasePackageDTO.class);
        // 数据填充
        dataFillService.fillRlatAttrForDataObj(dto);
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(BizContext ctx) {
        DicPurchasePackageSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改采购包管理 po：{}", JSONObject.toJSONString(po));
        this.check(po);
        DicPurchasePackage entity = UtilBean.newInstance(po, DicPurchasePackage.class);
        if (UtilNumber.isEmpty(po.getId())) {//新增
            entity.setCreateUserId(currentUser.getId());
            entity.setCreateTime(new Date());
            entity.setModifyUserId(currentUser.getId());
            entity.setModifyTime(new Date());
        } else {//修改
            entity.setModifyUserId(currentUser.getId());
            entity.setModifyTime(new Date());
        }
        if (dicPurchasePackageDataWrap.saveOrUpdate(entity)) {
            log.info("采购包管理：{}，保存成功", entity.getId());
        }
        //刷新缓存
        editCacheService.refreshPurchasePackageCacheById(entity.getId());
    }

    /**
     * 数据校验
     */
    private void check(DicPurchasePackageSavePO po){
        if (UtilObject.isNull(po)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNullOrEmpty(po.getPurchasePackageCode())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNullOrEmpty(po.getPurchasePackageName())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNullOrEmpty(po.getContractCode())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNullOrEmpty(po.getContractName())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilString.isNullOrEmpty(po.getPurchasePersonName())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        //采购包号唯一值校验
        QueryWrapper<DicPurchasePackage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicPurchasePackage::getPurchasePackageCode,po.getPurchasePackageCode());
        List<DicPurchasePackage> list = dicPurchasePackageDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(list)){
            return;
        }
        //若过滤掉当前数据，仍然存在与 po 采购包号相同的数据，则采购包号重复，返回提示语
        List<DicPurchasePackage> excludeModifyList = list.stream().filter(e -> !e.getId().equals(po.getId())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(excludeModifyList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_EXISTED, po.getPurchasePackageCode());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeById(Long id) {
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 逻辑删除
        if (dicPurchasePackageDataWrap.removeById(id)) {
            log.info("采购包管理：{}，删除成功", id);
            editCacheService.deletePurchasePackageCacheById(id);
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FAILURE, String.valueOf(id));
        }
    }

    public MultiResultVO<DicPurchasePackagePageVO> getList(BizContext ctx) {
        DicPurchasePackageSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("采购包主数据列表 po：{}", JSONObject.toJSONString(po));
        if (UtilObject.isNull(po)) {
            po = new DicPurchasePackageSearchPO();
        }
        List<DicPurchasePackagePageVO> list = dicPurchasePackageDataWrap.getList(po);
        return new MultiResultVO<>(list);
    }

    /**
     * 根据采购包号查询采购包
     */
    public DicPurchasePackageDTO getByPurchasePackageCode(String purchasePackageCode) {
        if (UtilString.isNullOrEmpty(purchasePackageCode)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        QueryWrapper<DicPurchasePackage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicPurchasePackage::getPurchasePackageCode,purchasePackageCode);
        List<DicPurchasePackage> list = dicPurchasePackageDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(list)) {
            return null;
        }
        DicPurchasePackageDTO dicPurchasePackageDTO = UtilBean.newInstance(list.get(0), DicPurchasePackageDTO.class);
        dataFillService.fillAttr(dicPurchasePackageDTO);
        return dicPurchasePackageDTO;
    }
}

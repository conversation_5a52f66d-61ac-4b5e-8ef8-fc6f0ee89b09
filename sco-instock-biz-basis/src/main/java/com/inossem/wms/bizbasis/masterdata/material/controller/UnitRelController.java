package com.inossem.wms.bizbasis.masterdata.material.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.unit.dto.DicUnitRelDTO;
import com.inossem.wms.common.model.masterdata.unit.po.DicUnitRelSavePO;
import com.inossem.wms.common.model.masterdata.unit.po.DicUnitRelSearchPO;
import com.inossem.wms.common.model.masterdata.unit.vo.DicUnitRelPageVO;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.UnitRelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 单位换算
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "单位关系管理")
public class UnitRelController {

    @Autowired
    protected UnitRelService unitRelService;

    /**
     * 获取单位关系列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 单位关系集合列表
     */
     @ApiOperation(value = "获取单位关系列表", tags = {"单位关系管理"})
     @PostMapping(path = "/master-data/unit-rel/results", produces = MediaType.APPLICATION_JSON_VALUE)
     public BaseResult<PageObjectVO<DicUnitRelPageVO>> getPage(@RequestBody DicUnitRelSearchPO po, BizContext ctx) {
     return BaseResult.success(unitRelService.getPage(ctx));
     }

    /**
     * 查看单位关系详情
     *
     * <AUTHOR>
     * @param id 单位关系Id
     * @return 单位关系详情
     */
    @ApiOperation(value = "按照单位关系编码查找单位关系", tags = {"单位关系管理"})
    @GetMapping(path = "/master-data/unit-rel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicUnitRelDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(unitRelService.get(ctx));
    }

    /**
     * 新增单位关系
     *
     * <AUTHOR>
     * @param po 单位关系入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增单位关系信息", notes = "对单位关系信息进行添加", tags = {"单位关系管理"})
    @PostMapping(path = "/master-data/unit-rel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicUnitRelSavePO po, BizContext ctx) {
        // 储单位关系信息
        unitRelService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_UNIT_REL_SAVE_SUCCESS);
    }

    /**
     * 新增或修改单位关系
     *
     * <AUTHOR>
     * @param po 单位关系入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增/修改单位关系信息", notes = "对单位关系信息进行添加、修改", tags = {"单位关系管理"})
    @PutMapping(path = "/master-data/unit-rel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicUnitRelSavePO po, BizContext ctx) {
        // 储单位关系信息
        unitRelService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_UNIT_REL_SAVE_SUCCESS);
    }

    /**
     * 删除单位关系
     *
     * @param id 单位关系Id
     * @param ctx 上下文对象
     * <AUTHOR>
     * @return 删除结果
     */
    @ApiOperation(value = "按照单位关系编码删除单位关系", notes = "逻辑删除", tags = {"单位关系管理"})
    @DeleteMapping(path = "/master-data/unit-rel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除单位关系信息
        unitRelService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_UNTI_REL_DELETE_SUCCESS);
    }
}

package com.inossem.wms.bizbasis.bi.restful.service;

import cn.hutool.core.date.DateUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.HXSapConst;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;

import com.inossem.wms.common.model.bizdomain.bi.entity.BiBseg;
import com.inossem.wms.common.model.erp.po.ErpBiSearchPO;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.common.util.sap.SapApiCallProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 华信SAP-BI接口服务类
 * 负责与SAP系统进行数据交互，同步BI大屏数据
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Service
@Slf4j
public class HXSapBiInterfaceService {

    @Autowired
    protected SapApiCallProxy sapApiCallProxy;
    


    /**************************************************************************
     ***************          签名方法，调用sap之前需要调用          **************
     **************************************************************************/

    private static final String CONCAT_KEY = "\n";
    private static final String INPUT_PARAMS = "input_params";
    private static final String SIGNATURE_PARAMS = "signature";
    private static final String SAP_PARAMS = "sap_params";
    private static final String SIGNATURE_CREATED = "signature_created";

    /**
     * 根据请求参数中的key排序，并将对应的value转换为字符串
     *
     * @param requestParams 请求参数
     * @return String 返回转换后的结果
     */
    public String sortToStr(JsonObject requestParams) {
        // 对参数进行排序
        Set<String> paramKeys = requestParams.keySet();
        String[] sortParamKeys = new String[paramKeys.size()];
        Arrays.sort(paramKeys.toArray(sortParamKeys));
        StringBuilder paramValueStr = new StringBuilder();

        // 将请求参数json转换为字符串，用于签名
        for (String paramKey : sortParamKeys) {
            if (SIGNATURE_CREATED.equals(paramKey)) {
                paramValueStr.append(CONCAT_KEY).append(requestParams.get(paramKey).getAsLong());
                continue;
            }
            paramValueStr.append(CONCAT_KEY).append(requestParams.get(paramKey).toString());
        }
        // 删除头部的连接符
        String paramValue = null;
        if (paramValueStr.toString().startsWith(CONCAT_KEY) && paramValueStr.length() >= 1) {
            paramValue = paramValueStr.substring(1);
        }
        return paramValue;
    }

    /**
     * 通过秘钥对字符串进行签名
     *
     * @param params 需要签名的字符串
     * @param secret 签名秘钥（指SAP中间件分配的系统秘钥
     * @return 返回签名后的结果
     */
    public String signature(String params, String secret) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256");
        hmacSHA256.init(secretKey);
        return Base64.encodeBase64String(hmacSHA256.doFinal(params.getBytes("UTF-8")));
    }

    /**
     * 构建接口请求体的内容，使用系统当前时间作为时间戳进行构建
     *
     * @param sapParams SAP BAPI函数的参数内容
     * @return 返回调用接口的请求体内容
     */
    public JsonObject buildRequestParams(JsonObject sapParams) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        // 时间戳
        Long timeStamp = System.currentTimeMillis() / 1000;
        log.debug("时间戳:{}", timeStamp);
        // 接口请求的输入参数
        JsonObject inputParams = new JsonObject();
        inputParams.add(SAP_PARAMS, sapParams);
        // 接口请求参数
        JsonObject requestParams = new JsonObject();
        requestParams.add(INPUT_PARAMS, inputParams);
        requestParams.addProperty(SIGNATURE_CREATED, timeStamp);
        // 将请求参数转换为String,用于签名使用
        String requestParamsStr = sortToStr(requestParams);
        log.debug("1、需要签名的字符串:{}", requestParamsStr.replace(CONCAT_KEY, " -- "));
        // 将请求参数进行签名
        String signature = signature(requestParamsStr, UtilConst.getInstance().getBiSecret());
        log.debug("2、参数签名结果:{}", signature);
        requestParams.addProperty(SIGNATURE_PARAMS, signature);
        log.debug("3、请求参数格式如下:{}", requestParams);
        return requestParams;
    }

    /**
     * 调用SAP接口，签名转换
     */
    private JsonObject callHXSapAPI(String erpUrl, JsonObject jsonObject) {
        try {
            JsonObject params = buildRequestParams(jsonObject);
//            return sapApiCallProxy.callHXSapApi(erpUrl, params);
            String responseStr = "{\n" +
                    "  \"success\": true,\n" +
                    "  \"data\": {\n" +
                    "    \"ET_OUT\": [\n" +
                    "      {\n" +
                    "        \"COMP_CODE\": \"1104\",\n" +
                    "        \"FISCYEAR\": \"2024\",\n" +
                    "        \"FISCVARNT\": \"K4\",\n" +
                    "        \"AC_DOC_NR\": \"**********\",\n" +
                    "        \"AC_DOC_LN\": \"000001\",\n" +
                    "        \"AC_LEDGER\": \"0L\",\n" +
                    "        \"PLANT\": \"\",\n" +
                    "        \"RECTYPE\": \"0\",\n" +
                    "        \"VERSION\": \"001\",\n" +
                    "        \"FISCPER\": \"2024011\",\n" +
                    "        \"RECORDMODE\": \"\",\n" +
                    "        \"AC_DOC_NO\": \"**********\",\n" +
                    "        \"ITEM_NUM\": \"000\",\n" +
                    "        \"FI_DBCRIND\": \"S\",\n" +
                    "        \"/BIC/ZUONR\": \"\",\n" +
                    "        \"POSTXT\": \"********** - ******** 上评估\",\n" +
                    "        \"GL_ACCOUNT\": \"**********\",\n" +
                    "        \"CHRT_ACCTS\": \"SECG\",\n" +
                    "        \"CUSTOMER\": \"\",\n" +
                    "        \"VENDOR\": \"\",\n" +
                    "        \"PCOMPANY\": \"\",\n" +
                    "        \"POST_KEY\": \"40\",\n" +
                    "        \"ACCT_TYPE\": \"S\",\n" +
                    "        \"/BIC/ZPSWSL\": \"\",\n" +
                    "        \"/BIC/ZVORGN\": \"\",\n" +
                    "        \"COSTCENTER\": \"\",\n" +
                    "        \"CO_AREA\": \"1104\",\n" +
                    "        \"COORDER\": \"\",\n" +
                    "        \"ASSET\": \"\",\n" +
                    "        \"ASSET_MAIN\": \"\",\n" +
                    "        \"/BIC/ZANBWA\": \"\",\n" +
                    "        \"/BIC/ZBZDAT\": null,\n" +
                    "        \"/BIC/ZMATERIAL\": \"\",\n" +
                    "        \"MATERIAL\": \"\",\n" +
                    "        \"PROFIT_CTR\": \"P1104\",\n" +
                    "        \"/BIC/ZNPLNR\": \"\",\n" +
                    "        \"WBS_ELEMT\": \"\",\n" +
                    "        \"/BIC/ZPAOBJNR\": \"0000000000\",\n" +
                    "        \"REF_KEY1\": \"\",\n" +
                    "        \"REF_KEY2\": \"\",\n" +
                    "        \"REF_KEY3\": \"\",\n" +
                    "        \"XNEGP\": \"\",\n" +
                    "        \"KIDNO\": \"\",\n" +
                    "        \"FUNC_AREA\": \"\",\n" +
                    "        \"FI_AWTYP\": \"BKPFF\",\n" +
                    "        \"PSM_AWKEY\": \"**********11042024\",\n" +
                    "        \"/BIC/ZH_WAERS\": \"\",\n" +
                    "        \"/BIC/ZH_BLART\": \"SA\",\n" +
                    "        \"/BIC/ZMATGROUP\": \"\",\n" +
                    "        \"MATL_GROUP\": \"\",\n" +
                    "        \"/BIC/ZWGBEZ_Z\": \"\",\n" +
                    "        \"/BIC/ZWGBEZ_E\": \"\",\n" +
                    "        \"/BIC/ZMAKTX_Z\": \"\",\n" +
                    "        \"/BIC/ZMAKTX_E\": \"\",\n" +
                    "        \"EVALGROUP1\": \"\",\n" +
                    "        \"CALYEAR\": \"2024\",\n" +
                    "        \"FISCPER3\": \"011\",\n" +
                    "        \"CALMONTH\": \"202411\",\n" +
                    "        \"CALMONTH2\": \"11\",\n" +
                    "        \"CREATEDON\": \"20241202\",\n" +
                    "        \"DOC_DATE\": \"********\",\n" +
                    "        \"PSTNG_DATE\": \"********\",\n" +
                    "        \"UPD_DATE\": \"20241202\",\n" +
                    "        \"CLEAR_DATE\": null,\n" +
                    "        \"CLR_DOC_FY\": \"0000\",\n" +
                    "        \"/BIC/ZVALUT\": null,\n" +
                    "        \"/BIC/ZH_BUDAT\": \"********\",\n" +
                    "        \"/BIC/ZH_BLDAT\": \"********\",\n" +
                    "        \"/BIC/ZFBDT\": null,\n" +
                    "        \"CPUDT\": \"20241202\",\n" +
                    "        \"AEDAT\": null,\n" +
                    "        \"/BIC/ZDMBTR\": \"1210.00\",\n" +
                    "        \"/BIC/ZWRBTR\": \"0.00\",\n" +
                    "        \"QUANTITY\": \"0.000\",\n" +
                    "        \"UNIT\": \"\",\n" +
                    "        \"DOC_CURRCY\": \"USD\",\n" +
                    "        \"LOC_CURRCY\": \"PKR\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"COMP_CODE\": \"1104\",\n" +
                    "        \"FISCYEAR\": \"2024\",\n" +
                    "        \"FISCVARNT\": \"K4\",\n" +
                    "        \"AC_DOC_NR\": \"**********\",\n" +
                    "        \"AC_DOC_LN\": \"000002\",\n" +
                    "        \"AC_LEDGER\": \"0L\",\n" +
                    "        \"PLANT\": \"\",\n" +
                    "        \"RECTYPE\": \"0\",\n" +
                    "        \"VERSION\": \"001\",\n" +
                    "        \"FISCPER\": \"2024011\",\n" +
                    "        \"RECORDMODE\": \"\",\n" +
                    "        \"AC_DOC_NO\": \"**********\",\n" +
                    "        \"ITEM_NUM\": \"000\",\n" +
                    "        \"FI_DBCRIND\": \"H\",\n" +
                    "        \"/BIC/ZUONR\": \"\",\n" +
                    "        \"POSTXT\": \"********** - ******** 上评估\",\n" +
                    "        \"GL_ACCOUNT\": \"**********\",\n" +
                    "        \"CHRT_ACCTS\": \"SECG\",\n" +
                    "        \"CUSTOMER\": \"\",\n" +
                    "        \"VENDOR\": \"\",\n" +
                    "        \"PCOMPANY\": \"\",\n" +
                    "        \"POST_KEY\": \"50\",\n" +
                    "        \"ACCT_TYPE\": \"S\",\n" +
                    "        \"/BIC/ZPSWSL\": \"\",\n" +
                    "        \"/BIC/ZVORGN\": \"\",\n" +
                    "        \"COSTCENTER\": \"\",\n" +
                    "        \"CO_AREA\": \"1104\",\n" +
                    "        \"COORDER\": \"\",\n" +
                    "        \"ASSET\": \"\",\n" +
                    "        \"ASSET_MAIN\": \"\",\n" +
                    "        \"/BIC/ZANBWA\": \"\",\n" +
                    "        \"/BIC/ZBZDAT\": null,\n" +
                    "        \"/BIC/ZMATERIAL\": \"\",\n" +
                    "        \"MATERIAL\": \"\",\n" +
                    "        \"PROFIT_CTR\": \"P1104\",\n" +
                    "        \"/BIC/ZNPLNR\": \"\",\n" +
                    "        \"WBS_ELEMT\": \"\",\n" +
                    "        \"/BIC/ZPAOBJNR\": \"0000000000\",\n" +
                    "        \"REF_KEY1\": \"\",\n" +
                    "        \"REF_KEY2\": \"\",\n" +
                    "        \"REF_KEY3\": \"\",\n" +
                    "        \"XNEGP\": \"\",\n" +
                    "        \"KIDNO\": \"\",\n" +
                    "        \"FUNC_AREA\": \"\",\n" +
                    "        \"FI_AWTYP\": \"BKPFF\",\n" +
                    "        \"PSM_AWKEY\": \"**********11042024\",\n" +
                    "        \"/BIC/ZH_WAERS\": \"\",\n" +
                    "        \"/BIC/ZH_BLART\": \"SA\",\n" +
                    "        \"/BIC/ZMATGROUP\": \"\",\n" +
                    "        \"MATL_GROUP\": \"\",\n" +
                    "        \"/BIC/ZWGBEZ_Z\": \"\",\n" +
                    "        \"/BIC/ZWGBEZ_E\": \"\",\n" +
                    "        \"/BIC/ZMAKTX_Z\": \"\",\n" +
                    "        \"/BIC/ZMAKTX_E\": \"\",\n" +
                    "        \"EVALGROUP1\": \"\",\n" +
                    "        \"CALYEAR\": \"2024\",\n" +
                    "        \"FISCPER3\": \"011\",\n" +
                    "        \"CALMONTH\": \"202411\",\n" +
                    "        \"CALMONTH2\": \"11\",\n" +
                    "        \"CREATEDON\": \"20241202\",\n" +
                    "        \"DOC_DATE\": \"********\",\n" +
                    "        \"PSTNG_DATE\": \"********\",\n" +
                    "        \"UPD_DATE\": \"20241202\",\n" +
                    "        \"CLEAR_DATE\": null,\n" +
                    "        \"CLR_DOC_FY\": \"0000\",\n" +
                    "        \"/BIC/ZVALUT\": null,\n" +
                    "        \"/BIC/ZH_BUDAT\": \"********\",\n" +
                    "        \"/BIC/ZH_BLDAT\": \"********\",\n" +
                    "        \"/BIC/ZFBDT\": null,\n" +
                    "        \"CPUDT\": \"20241202\",\n" +
                    "        \"AEDAT\": null,\n" +
                    "        \"/BIC/ZDMBTR\": \"-1210.00\",\n" +
                    "        \"/BIC/ZWRBTR\": \"0.00\",\n" +
                    "        \"QUANTITY\": \"0.000\",\n" +
                    "        \"UNIT\": \"\",\n" +
                    "        \"DOC_CURRCY\": \"USD\",\n" +
                    "        \"LOC_CURRCY\": \"PKR\"\n" +
                    "      }\n" +
                    "    ],\n" +
                    "    \"IT_DATE\": [\n" +
                    "      {\n" +
                    "        \"SIGN\": \"I\",\n" +
                    "        \"OPTION\": \"BT\",\n" +
                    "        \"LOW\": \"20241202\",\n" +
                    "        \"HIGH\": \"20241203\"\n" +
                    "      }\n" +
                    "    ],\n" +
                    "    \"RETURN\": {\n" +
                    "      \"TYPE\": \"S\",\n" +
                    "      \"ID\": \"\",\n" +
                    "      \"NUMBER\": \"000\",\n" +
                    "      \"MESSAGE\": \"查询成功\",\n" +
                    "      \"LOG_NO\": \"\",\n" +
                    "      \"LOG_MSG_NO\": \"000000\",\n" +
                    "      \"MESSAGE_V1\": \"\",\n" +
                    "      \"MESSAGE_V2\": \"\",\n" +
                    "      \"MESSAGE_V3\": \"\",\n" +
                    "      \"MESSAGE_V4\": \"\",\n" +
                    "      \"PARAMETER\": \"\",\n" +
                    "      \"ROW\": 0,\n" +
                    "      \"FIELD\": \"\",\n" +
                    "      \"SYSTEM\": \"\"\n" +
                    "    }\n" +
                    "  },\n" +
                    "  \"message\": \"success\"\n" +
                    "}\n";
            return JsonParser.parseString(responseStr).getAsJsonObject();
        } catch (Exception e) {
            log.error("调用SAP接口失败，错误信息：{}", e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, e.getMessage());
        }
    }

    /**
     * 构建入参
     *
     * @param po 查询入参
     * @return JsonObject
     */
    private JsonObject generateRequestParams(ErpBiSearchPO po) {
        // 组装参数
        JsonObject params = new JsonObject();
        JsonArray itDate = new JsonArray();
        JsonObject element = new JsonObject();
        element.addProperty("SIGN", "I");
        element.addProperty("OPTION", "BT");
        element.addProperty("LOW", po.getUpdDateLow());
        element.addProperty("HIGH", po.getUpdDateHigh());
        itDate.add(element);
        params.addProperty("I_BUKRS", po.getCompCode());
        params.add("IT_DATE", itDate);
        return params;
    }

    /**
     * 统一处理SAP返回结果
     * 检查接口调用状态，非成功状态抛出异常
     *
     * @param response      SAP接口返回的JSON响应
     * @throws WmsException 当接口调用失败时抛出异常
     */
    private void handleSapResponse(JsonObject response) {
        if (!response.isEmpty() && response.get("success").getAsBoolean()) {
            JsonObject data = response.get("data").getAsJsonObject();
            JsonObject returnObject = new JsonObject();
            if (data.has("E_RETURN")) {
                returnObject = data.get("E_RETURN").getAsJsonObject();
            } else if (data.has("RETURN")) {
                // 供应商接口特殊， 不知道为啥返了个集合
                if (data.get("RETURN") instanceof JsonObject) {
                    returnObject = data.get("RETURN").getAsJsonObject();
                } else if (data.get("RETURN") instanceof JsonArray) {
                    JsonArray jsonArray = data.get("RETURN").getAsJsonArray();
                    if (jsonArray.isJsonNull() || jsonArray.size() == 0) {
                        returnObject.addProperty("TYPE", "E");
                        returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
                    } else {
                        returnObject = jsonArray.get(0).getAsJsonObject();
                    }
                } else {
                    returnObject.addProperty("TYPE", "E");
                    returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
                }
            } else {
                returnObject.addProperty("TYPE", "E");
                returnObject.addProperty("MESSAGE", "SAP接口返回数据格式错误");
            }
            response.addProperty("TYPE", UtilObject.getStringOrEmpty(returnObject.get("TYPE")));

            if (!Const.ERP_RETURN_TYPE_S.equals(returnObject.get("TYPE").getAsString())) {
                String errorMsg = UtilObject.getStringOrEmpty(returnObject.get("MESSAGE").getAsString());
                log.error("SAP接口调用失败 - , 错误信息: {}", errorMsg);
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, errorMsg);
            }

        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, response.get("message").getAsString() == null ? "调用失败，SAP返回信息为空" : response.get("message").getAsString());
        }
    }

    /**
     * 同步SAP-BI-BSEG信息
     *
     * @param po 查询参数PO
     * @return List<BiBseg> 解析后的BiBseg数据列表
     * @throws WmsException 接口调用失败时抛出异常
     */
    public List<BiBseg> synBiBsegInfo(ErpBiSearchPO po) {
        log.info("开始同步BiBseg信息从SAP, 公司代码: {}, 起始日期: {}, 截止日期: {}", po.getCompCode(), po.getUpdDateLow(), po.getUpdDateHigh());

        // 构建入参
        JsonObject params = this.generateRequestParams(po);

        log.debug("调用SAP-BI获取BI-BSEG信息:{}", params);

        String erpUrl = UtilConst.getInstance().getBiUrl() + HXSapConst.ZFM_FICO_001;
        JsonObject returnObject = this.callHXSapAPI(erpUrl, params);

        // 处理返回结果
        this.handleSapResponse(returnObject);

        // 解析出参
        List<BiBseg> biBsegList = new ArrayList<>();
        if (returnObject.get("data").getAsJsonObject().has("ET_OUT")) {
            JsonArray etOut = returnObject.getAsJsonObject("data").getAsJsonArray("ET_OUT");
            for (JsonElement jsonElement : etOut) {
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                BiBseg biBseg = new BiBseg();

                // 基本字段映射
                biBseg.setBukrs(UtilObject.getStringOrNull(jsonObject.get("COMP_CODE"))); // 公司代码
                biBseg.setGjahr(UtilObject.getIntegerOrNull(jsonObject.get("FISCYEAR"))); // 会计年度
                biBseg.setFiscvarnt(UtilObject.getStringOrNull(jsonObject.get("FISCVARNT"))); // 会计年度变式
                biBseg.setBelnr(UtilObject.getStringOrNull(jsonObject.get("AC_DOC_NR"))); // 会计凭证编号
                biBseg.setBuzei(UtilObject.getIntegerOrNull(jsonObject.get("AC_DOC_LN"))); // 会计凭证行项目号
                biBseg.setRldnr(UtilObject.getStringOrNull(jsonObject.get("AC_LEDGER"))); // 分类账
                biBseg.setWerks(UtilObject.getStringOrNull(jsonObject.get("PLANT"))); // 工厂
                biBseg.setRrcty(UtilObject.getStringOrNull(jsonObject.get("RECTYPE"))); // 记录类型
                biBseg.setRvers(UtilObject.getStringOrNull(jsonObject.get("VERSION"))); // 版本
                biBseg.setFiscper(UtilObject.getStringOrNull(jsonObject.get("FISCPER"))); // 会计期间
                biBseg.setRecordmode(UtilObject.getStringOrNull(jsonObject.get("RECORDMODE"))); // 记录模式
                biBseg.setAcDocNo(UtilObject.getStringOrNull(jsonObject.get("AC_DOC_NO"))); // 会计凭证编号
                biBseg.setAcItemNum(UtilObject.getStringOrNull(jsonObject.get("ITEM_NUM"))); // 行项目号
                biBseg.setShkzg(UtilObject.getStringOrNull(jsonObject.get("FI_DBCRIND"))); // 借贷标识
                biBseg.setZuonr(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZUONR"))); // 分配号码
                biBseg.setSgtxt(UtilObject.getStringOrNull(jsonObject.get("POSTXT"))); // 行项目文本
                biBseg.setHkont(UtilObject.getStringOrNull(jsonObject.get("GL_ACCOUNT"))); // 总账科目
                biBseg.setKtopl(UtilObject.getStringOrNull(jsonObject.get("CHRT_ACCTS"))); // 科目表
                biBseg.setKunnr(UtilObject.getStringOrNull(jsonObject.get("CUSTOMER"))); // 客户编号
                biBseg.setLifnr(UtilObject.getStringOrNull(jsonObject.get("VENDOR"))); // 供应商编号
                biBseg.setVbund(UtilObject.getStringOrNull(jsonObject.get("PCOMPANY"))); // 合作伙伴公司代码
                biBseg.setBschl(UtilObject.getStringOrNull(jsonObject.get("POST_KEY"))); // 过账码
                biBseg.setKoart(UtilObject.getStringOrNull(jsonObject.get("ACCT_TYPE"))); // 科目类型
                biBseg.setPswsl(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZPSWSL"))); // 产品币种
                biBseg.setVorgn(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZVORGN"))); // 业务类型
                biBseg.setKostl(UtilObject.getStringOrNull(jsonObject.get("COSTCENTER"))); // 成本中心
                biBseg.setKokrs(UtilObject.getStringOrNull(jsonObject.get("CO_AREA"))); // 控制范围
                biBseg.setAufnr(UtilObject.getStringOrNull(jsonObject.get("COORDER"))); // 订单号
                biBseg.setAnln2(UtilObject.getStringOrNull(jsonObject.get("ASSET"))); // 资产子编号
                biBseg.setAnln1(UtilObject.getStringOrNull(jsonObject.get("ASSET_MAIN"))); // 资产主编号
                biBseg.setAnbwa(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZANBWA"))); // 资产业务类型
                biBseg.setBzdat(UtilString.getDateFromJsonElement(jsonObject.get("/BIC/ZBZDAT"))); // 资产评估日期
                biBseg.setZmaterial(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZMATERIAL"))); // 物料编号
                biBseg.setMatnr(UtilObject.getStringOrNull(jsonObject.get("MATERIAL"))); // 物料编号
                biBseg.setPrctr(UtilObject.getStringOrNull(jsonObject.get("PROFIT_CTR"))); // 利润中心
                biBseg.setNplnr(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZNPLNR"))); // 网络编号
                biBseg.setProjk(UtilObject.getStringOrNull(jsonObject.get("WBS_ELEMT"))); // WBS元素
                biBseg.setPaobjnr(UtilObject.getIntegerOrNull(jsonObject.get("/BIC/ZPAOBJNR"))); // 获利对象编号
                biBseg.setXref1(UtilObject.getStringOrNull(jsonObject.get("REF_KEY1"))); // 参考码1
                biBseg.setXref2(UtilObject.getStringOrNull(jsonObject.get("REF_KEY2"))); // 参考码2
                biBseg.setXref3(UtilObject.getStringOrNull(jsonObject.get("REF_KEY3"))); // 参考码3
                biBseg.setXnegp(UtilObject.getStringOrNull(jsonObject.get("XNEGP"))); // 反记账标识
                biBseg.setKidno(UtilObject.getStringOrNull(jsonObject.get("KIDNO"))); // 文档编号
                biBseg.setFkber(UtilObject.getStringOrNull(jsonObject.get("FUNC_AREA"))); // 功能范围
                biBseg.setAwtyp(UtilObject.getStringOrNull(jsonObject.get("FI_AWTYP"))); // 参考事务类型
                biBseg.setAwkey(UtilObject.getStringOrNull(jsonObject.get("PSM_AWKEY"))); // 参考凭证编号
                biBseg.setHWaers(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZH_WAERS"))); // 本位币
                biBseg.setHBlart(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZH_BLART"))); // 凭证类型
                biBseg.setZmatgroup(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZMATGROUP"))); // 物料组
                biBseg.setMatkl(UtilObject.getStringOrNull(jsonObject.get("MATL_GROUP"))); // 物料组
                biBseg.setWgbez(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZWGBEZ_Z"))); // 物料组描述
                biBseg.setWgbezEn(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZWGBEZ_E"))); // 物料组描述(英文)
                biBseg.setMaktx(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZMAKTX_Z"))); // 物料描述
                biBseg.setMaktxEn(UtilObject.getStringOrNull(jsonObject.get("/BIC/ZMAKTX_E"))); // 物料描述(英文)
                biBseg.setCalyear(UtilObject.getStringOrNull(jsonObject.get("CALYEAR"))); // 日历年度
                biBseg.setFiscper3(UtilObject.getStringOrNull(jsonObject.get("FISCPER3"))); // 会计期间
                biBseg.setCalmonth(UtilObject.getStringOrNull(jsonObject.get("CALMONTH"))); // 日历月份
                biBseg.setCalmonth2(UtilObject.getStringOrNull(jsonObject.get("CALMONTH2"))); // 日历月份
                biBseg.setCreatedon(UtilString.getDateFromJsonElement(jsonObject.get("CREATEDON"))); // 创建日期
                biBseg.setBldat(UtilString.getDateFromJsonElement(jsonObject.get("DOC_DATE"))); // 凭证日期
                biBseg.setPstngDate(UtilString.getDateFromJsonElement(jsonObject.get("PSTNG_DATE"))); // 过账日期
                biBseg.setUpdDate(UtilString.getDateFromJsonElement(jsonObject.get("UPD_DATE"))); // 更新日期
                biBseg.setAugdt(UtilString.getDateFromJsonElement(jsonObject.get("CLEAR_DATE"))); // 清账日期
                biBseg.setAuggj(UtilObject.getIntegerOrNull(jsonObject.get("CLR_DOC_FY"))); // 清账会计年度
                biBseg.setValut(UtilString.getDateFromJsonElement(jsonObject.get("/BIC/ZVALUT"))); // 起息日
                biBseg.setHBudat(UtilString.getDateFromJsonElement(jsonObject.get("/BIC/ZH_BUDAT"))); // 过账日期
                biBseg.setBudat(UtilString.getDateFromJsonElement(jsonObject.get("/BIC/ZH_BLDAT"))); // 凭证日期
                biBseg.setZfbdt(UtilString.getDateFromJsonElement(jsonObject.get("/BIC/ZFBDT"))); // 基准日期
                biBseg.setCpudt(UtilString.getDateFromJsonElement(jsonObject.get("CPUDT"))); // 输入日期
                biBseg.setAedat(UtilString.getDateFromJsonElement(jsonObject.get("AEDAT"))); // 最后修改日期
                biBseg.setDmbtr(UtilObject.getBigDecimalOrZero(jsonObject.get("/BIC/ZDMBTR")).abs()); // 本位币金额
                biBseg.setWrbtr(UtilObject.getBigDecimalOrZero(jsonObject.get("/BIC/ZWRBTR"))); // 交易币种金额
                biBseg.setMenge(UtilObject.getBigDecimalOrZero(jsonObject.get("QUANTITY"))); // 数量
                biBseg.setMeins(UtilObject.getStringOrNull(jsonObject.get("UNIT"))); // 基本计量单位
                biBseg.setWaers(UtilObject.getStringOrNull(jsonObject.get("DOC_CURRCY"))); // 凭证币种
                biBseg.setHwaer(UtilObject.getStringOrNull(jsonObject.get("LOC_CURRCY"))); // 本位币种

                biBseg.setYear(DateUtil.year(biBseg.getHBudat())); // 年份
                biBseg.setMonth(DateUtil.month(biBseg.getHBudat())); // 月份

                biBsegList.add(biBseg);
            }
        }

        log.info("BiBseg信息解析完成, 共解析: {} 条", biBsegList.size());
        return biBsegList;
    }

}

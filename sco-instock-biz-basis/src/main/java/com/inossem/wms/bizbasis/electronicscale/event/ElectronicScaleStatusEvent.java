package com.inossem.wms.bizbasis.electronicscale.event;

import com.inossem.wms.common.model.common.base.BizContext;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @desc 电子秤状态变化事件源（在线 or 离线）
 *
 * <AUTHOR>
 */
@Getter
public class ElectronicScaleStatusEvent extends ApplicationEvent {

    private BizContext bizContext;

    public ElectronicScaleStatusEvent(Object source) {
        super(source);
    }

    public ElectronicScaleStatusEvent(Object source,BizContext bizContext) {
        super(source);
        this.bizContext = bizContext;
    }
}

package com.inossem.wms.bizbasis.masterdata.purchasepackage.service.biz;

import com.inossem.wms.bizbasis.masterdata.purchasepackage.service.biz.component.DicPurchasePackageComponent;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.purchasepackage.dto.DicPurchasePackageDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购包管理 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Slf4j
@Service
public class DicPurchasePackageService {

    @Autowired
    private DicPurchasePackageComponent dicPurchasePackageComponent;

    /**
     * 获取采购包管理 - 分页列表
     */
    public PageObjectVO<DicPurchasePackagePageVO> getPage(BizContext ctx) {
        return dicPurchasePackageComponent.getPage(ctx);
    }

    /**
     * 获取采购包管理详情
     */
    public SingleResultVO<DicPurchasePackageDTO> get(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 设置按钮组权限
        DicPurchasePackageDTO dto = dicPurchasePackageComponent.get(id);
        return new SingleResultVO<>(dto);
    }

    /**
     * 新增或修改方法
     */
    public void addOrUpdate(BizContext ctx) {
        dicPurchasePackageComponent.addOrUpdate(ctx);
    }

    /**
     * 删除方法
     */
    public String remove(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("采购包管理删除 matId：{}", id);
        dicPurchasePackageComponent.removeById(id);
        return String.valueOf(id);
    }

    /**
     * 查询列表
     */
    public MultiResultVO<DicPurchasePackagePageVO> getList(BizContext ctx) {
        return dicPurchasePackageComponent.getList(ctx);
    }

    /**
     * 根据采购包号查询采购包
     */
    public SingleResultVO getByPurchasePackageCode(String purchasePackageCode) {
        DicPurchasePackageDTO dto = dicPurchasePackageComponent.getByPurchasePackageCode(purchasePackageCode);
        return new SingleResultVO(dto);
    }
}

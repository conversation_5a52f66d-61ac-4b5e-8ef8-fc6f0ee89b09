package com.inossem.wms.bizbasis.electronicscale.event.Listener;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.electronicscale.event.ElectronicScaleWeightEvent;
import com.inossem.wms.bizbasis.websocket.ElectronicScaleSocketEndpoint;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.electronicscale.dto.LogElectronicScaleRecordDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import com.inossem.wms.common.util.UtilObject;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @desc 电子秤数据推送pda监听器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ElectronicScaleDataSendToPda {


    @Autowired
    private ElectronicScaleSocketEndpoint electronicScaleSocketEndpoint;


    @EventListener(classes = {ElectronicScaleWeightEvent.class})
    public void sendData(ElectronicScaleWeightEvent event) {
        BizContext ctx = event.getBizContext();
        LogElectronicScaleRecordDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        log.info(">>>>>>>PDA端事件监听接收到电子秤重量变化，po: {}", JSONObject.toJSONString(po));

        //判断电子秤与pda是否在通讯中
        DicWhStorageCellDTO communicationEws = electronicScaleSocketEndpoint.inCommunicationEws(po.getElectronicScaleId());
        if (UtilObject.isNotEmpty(communicationEws)) {//通讯中
            log.info("PDA连接中... PO：{}", JSONObject.toJSONString(communicationEws));

            if(Objects.isNull(communicationEws.getNetWeight())){//用上报平台的数据变化计算净重
                BigDecimal netWeight =  po.getVariableWeight().divide(new BigDecimal("1000"),3,BigDecimal.ROUND_HALF_UP).abs();
                po.setCurrentWeight(po.getCurrentWeight().divide(new BigDecimal("1000"),3,BigDecimal.ROUND_HALF_UP));
                po.setVariableWeight(po.getVariableWeight().divide(new BigDecimal("1000"),3,BigDecimal.ROUND_HALF_UP).abs());
                po.setNetWeight(netWeight);
                po.setVariableQty(new BigDecimal("1"));
                log.info("净重计算结果：{}",JSONObject.toJSONString(po));
                electronicScaleSocketEndpoint.pushShowData(po);
                return;
            }
            if(Objects.isNull(communicationEws.getWeight())){//未获取到库存重量
                log.error("#######未获取到当前库存重量,产生原因：(1)websocket未触发库存重量查询请求 （2）数据库查询延时");
                return;
            }
            //******************************************  通过库存重量计算操作数量 start  ***********************************
            //获取物料的标准重量及库存重量 start
            BigDecimal weight = communicationEws.getWeight().multiply(new BigDecimal("1000"));//库存重量 存储单位kg--转换为g
            BigDecimal netWeight =communicationEws.getNetWeight().multiply(new BigDecimal("1000"));//标准重量 存储单位kg --转换为g
            //获取物料的标准重量及库存重量 end

            BigDecimal realTimeWeight = po.getCurrentWeight();//实时重量 计量单位：g
            BigDecimal variableWeight = weight.subtract(realTimeWeight).abs();//重量变化值 g

            BigDecimal variableQty = variableWeight.divide(netWeight,0,BigDecimal.ROUND_HALF_UP);//  变化重量/净重 = 数量（四舍五入取整）

            if(variableQty.compareTo(po.getVariableQty()) != 0){
                log.warn("电子秤上报平台推送变化数量为 variableQty:{},通过公式计算variableQty：{},变化数量不一致,pda端使用公式计算变化数量（|(库存重量-当前重量)|/标准重量 ）",po.getVariableQty(),variableQty);
                po.setVariableQty(variableQty);
            }

            if(variableWeight.compareTo(po.getVariableWeight()) != 0){
                log.warn("电子秤上报平台推送变化重量为 variableWeight:{},通过库存重量公式计算variableWeight：{},变化值不一致,pda端使用公式计算（|(库存重量-当前重量)| ）",po.getVariableWeight(),variableWeight);
                po.setVariableWeight(variableWeight);
            }

            unitConversion(po);
            //重量、数量变化实时推送pda
            electronicScaleSocketEndpoint.pushShowData(po);
            //******************************************  通过库存重量计算操作数量 end  ***********************************
        } else {//未通讯
            log.info("当前电子秤 po: {} 重量变化数据不需要实时推送pda");
        }

    }
    //单位转换 g-->kg
    private void unitConversion(LogElectronicScaleRecordDTO po){
        BigDecimal currentWeight = po.getCurrentWeight().divide(new BigDecimal("1000"),3,BigDecimal.ROUND_HALF_UP);
        BigDecimal variableWeight = po.getVariableWeight().divide(new BigDecimal("1000"),3,BigDecimal.ROUND_HALF_UP);
        po.setCurrentWeight(currentWeight);
        po.setVariableWeight(variableWeight);

    }
}

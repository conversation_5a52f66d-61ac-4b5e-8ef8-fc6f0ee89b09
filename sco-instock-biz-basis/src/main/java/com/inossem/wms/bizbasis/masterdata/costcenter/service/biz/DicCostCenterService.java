package com.inossem.wms.bizbasis.masterdata.costcenter.service.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.inossem.wms.bizbasis.masterdata.costcenter.service.component.DicCostCenterComponent;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 成本中心管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@Service
public class DicCostCenterService {

    @Autowired
    protected DicCostCenterComponent dicCostCenterComponent;

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        dicCostCenterComponent.getPage(ctx);
    }

    /**
     * 同步成本中心
     */
    public void sync(BizContext ctx) {
        dicCostCenterComponent.sync(ctx);
    }
} 
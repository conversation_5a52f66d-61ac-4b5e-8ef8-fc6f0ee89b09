package com.inossem.wms.bizbasis.electronicscale.service.biz;

import com.inossem.wms.bizbasis.electronicscale.service.conmponent.LogElectronicScaleRecordComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.bizdomain.electronicscale.dto.LogElectronicScaleRecordDTO;
import com.inossem.wms.common.model.bizdomain.electronicscale.po.LogElectronicScaleRecordSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 *  电子秤移动记录 service
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@Service
public class LogElectronicScaleRecordService {
    @Autowired
    private LogElectronicScaleRecordComponent logElectronicScaleRecordComponent;

    /**
     * 电子秤移动记录-分页
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"logElectronicScaleRecordComponent#getPage"})
    public void getPage(BizContext ctx) {
        logElectronicScaleRecordComponent.getPage(ctx);
    }

    /**
     * 电子秤移动记录-上报
     *
     * @param ctx 入参上下文
     */
    @Entrance(call = {"logElectronicScaleRecordComponent#checkElectronicScaleRecordReport"})
    public void electronicScaleRecordReport(BizContext ctx) {
        // 上报数据校验
        logElectronicScaleRecordComponent.checkElectronicScaleRecordReport(ctx);
    }

    /**
     * 电子秤移动记录-上报
     *
     * @param ctx 入参上下文
     */
    @WmsMQListener(tags = TagConst.ELECTRONIC_SCALE_RECORD_REPORT)
    @Entrance(call = {"logElectronicScaleRecordComponent#multicastEvent"})
    public void electronicScaleRecordDataHandle(BizContext ctx) {
        // 发布电子秤重量变更事件
        logElectronicScaleRecordComponent.multicastEvent(ctx);
    }

    /**
     * 电子秤移动记录-电子秤最新一条记录
     *
     * @param po 查询条件
     */
    public LogElectronicScaleRecordDTO getLastOne(LogElectronicScaleRecordSearchPO po) {
        // 校验电子秤
        logElectronicScaleRecordComponent.checkElectronicScale(po);
        // 获取电子秤最新一条记录
        return logElectronicScaleRecordComponent.getLastOne(po);
    }
}

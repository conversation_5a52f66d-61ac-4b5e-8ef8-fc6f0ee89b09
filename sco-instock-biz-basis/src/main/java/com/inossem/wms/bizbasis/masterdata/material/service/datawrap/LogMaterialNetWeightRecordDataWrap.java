package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.common.model.bizdomain.collection.vo.BizReceiptCollectionTaskVO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.LogMaterialNetWeightRecord;
import com.inossem.wms.bizbasis.masterdata.material.dao.LogMaterialNetWeightRecordMapper;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.LogMaterialNetWeightRecordVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-17
 */
@Service
public class LogMaterialNetWeightRecordDataWrap extends BaseDataWrap<LogMaterialNetWeightRecordMapper, LogMaterialNetWeightRecord> {
    @Autowired
    private DataFillService dataFillService;

    /**
     * 物料净重变更记录-分页
     *
     * @param pageData     分页数据
     * @param pageWrapper  分页查询条件
     * @return IPage<LogMaterialNetWeightRecordVO>
     */
    public IPage<LogMaterialNetWeightRecordVO> getLogMaterialNetWeightRecordList(IPage<LogMaterialNetWeightRecordVO> pageData,
                                                  WmsQueryWrapper<LogMaterialNetWeightRecordSearchPO> pageWrapper) {
        List<LogMaterialNetWeightRecordVO> logMaterialNetWeightRecordList = this.baseMapper.getLogMaterialNetWeightRecordList(pageData, pageWrapper);
        dataFillService.fillAttr(logMaterialNetWeightRecordList);
        return pageData.setRecords(logMaterialNetWeightRecordList);
    }
}

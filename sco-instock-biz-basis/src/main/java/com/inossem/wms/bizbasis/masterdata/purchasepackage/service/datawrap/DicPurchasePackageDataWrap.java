package com.inossem.wms.bizbasis.masterdata.purchasepackage.service.datawrap;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.purchasepackage.dao.DicPurchasePackageMapper;
import com.inossem.wms.common.model.masterdata.purchasepackage.dto.DicPurchasePackageDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSearchPO;
import com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采购包管理 服务接口
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Service
public class DicPurchasePackageDataWrap extends BaseDataWrap<DicPurchasePackageMapper, DicPurchasePackage> {

    /**
     * 分页查询
     */
    public IPage<DicPurchasePackagePageVO> getPageVoList(IPage<DicPurchasePackagePageVO> page, DicPurchasePackageSearchPO po) {
        return page.setRecords(this.baseMapper.selectPageVoList(page, po));
    }

    /**
     * 查询列表
     */
    public List<DicPurchasePackagePageVO> getList(DicPurchasePackageSearchPO po) {
        return this.baseMapper.selectPageVoList(po);
    }
}

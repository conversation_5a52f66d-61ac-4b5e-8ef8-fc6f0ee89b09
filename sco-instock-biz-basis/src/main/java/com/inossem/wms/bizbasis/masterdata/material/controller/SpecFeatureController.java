package com.inossem.wms.bizbasis.masterdata.material.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.material.service.biz.SpecFeatureService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.spec.SpecFeatureMapVo;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureDTO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecFeatureSavePO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecFeatureSearchPO;
import com.inossem.wms.common.model.masterdata.spec.vo.BizSpecFeaturePageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 特性表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */
@RestController
@Slf4j
@Api(tags = "特性管理")
public class SpecFeatureController {

    @Autowired
    protected SpecFeatureService specFeatureService;

    /**
     * 查询特性列表
     *
     * @param po 查询条件
     * @return 特性列表
     *
     */
    @ApiOperation(value = "查询特性列表", tags = {"特性管理"})
    @PostMapping(path = "/master-data/spec-features/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizSpecFeaturePageVO>> getPage(@RequestBody BizSpecFeatureSearchPO po, BizContext ctx) {
        return BaseResult.success(specFeatureService.getPage(ctx));
    }

    /**
     * 查看特性详情
     *
     * @param id 主键id
     * @return 特性详情
     *
     */
    @ApiOperation(value = "查看特性详情", tags = {"特性管理"})
    @GetMapping(path = "/master-data/spec-features/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<BizSpecFeatureDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(specFeatureService.getInfo(ctx));
    }

    /**
     * 新增或修改特性信息
     *
     * @param po 查询条件
     * @param ctx-cUser 当前登录人信息
     *
     */
    @ApiOperation(value = "新增或修改特性信息", tags = {"特性管理"})
    @PostMapping(path = "/master-data/spec-features", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> saveOrUpdate(@RequestBody BizSpecFeatureSavePO po, BizContext ctx) {
        specFeatureService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SPEC_FEATURE_SAVE_SUCCESS, po.getBizSpecFeatureInfo().getSpecFeatureCode());
    }

    /**
     * 删除特性信息
     *
     * @param id 主键Id
     *
     */
    @ApiOperation(value = "删除特性信息", tags = {"特性管理"})
    @DeleteMapping(path = "/master-data/spec-features/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String featureCode = specFeatureService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SPEC_FEATURE_DELETE_SUCCESS, featureCode);
    }

    /**
     * 查询特性数据类型下拉
     *
     * @return 特性数据类型下拉框
     */
    @ApiOperation(value = "查询特性数据类型下拉", tags = {"特性管理"})
    @GetMapping(path = "/master-data/spec-features/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecFeatureMapVo>> getDown() {
        return BaseResult.success(specFeatureService.getDown());
    }
}

package com.inossem.wms.bizbasis.masterdata.costcenter.service.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.masterdata.costcenter.service.datawrap.DicCostCenterDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.costcenter.entity.DicCostCenter;
import com.inossem.wms.common.model.masterdata.costcenter.po.DicCostCenterSearchPO;
import com.inossem.wms.common.util.UtilString;

import lombok.extern.slf4j.Slf4j;

/**
 * 成本中心管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@Component
public class DicCostCenterComponent {

    @Autowired
    protected DicCostCenterDataWrap dicCostCenterDataWrap;

    @Autowired
    protected HXSapIntegerfaceService hxSapIntegerfaceService;

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 获取查询条件
        DicCostCenterSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        // 构建查询条件
        QueryWrapper<DicCostCenter> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getCostCenterCode()), DicCostCenter::getCostCenterCode, po.getCostCenterCode())
            .like(UtilString.isNotNullOrEmpty(po.getCostCenterName()), DicCostCenter::getCostCenterName, po.getCostCenterName());
            
        // 执行查询
        IPage<DicCostCenter> page = dicCostCenterDataWrap.page(po.getPageObj(DicCostCenter.class), queryWrapper);
        
        // 设置返回结果
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 同步成本中心
     */
    public void sync(BizContext ctx) {
        log.info("开始同步成本中心数据");
        
        // 没有接口
        
        log.info("成本中心数据同步完成");
    }
} 
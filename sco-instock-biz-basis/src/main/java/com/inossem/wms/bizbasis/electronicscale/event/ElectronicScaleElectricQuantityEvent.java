package com.inossem.wms.bizbasis.electronicscale.event;

import com.inossem.wms.common.model.common.base.BizContext;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @desc 电子秤电量变化事件源
 *
 * <AUTHOR>
 */
@Getter
public class ElectronicScaleElectricQuantityEvent  extends ApplicationEvent {

    private BizContext bizContext;

    public ElectronicScaleElectricQuantityEvent(Object source) {
        super(source);
    }

    public ElectronicScaleElectricQuantityEvent(Object source,BizContext bizContext) {
        super(source);
        this.bizContext = bizContext;
    }
}

package com.inossem.wms.bizbasis.electronicscale.service.conmponent;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Queues;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.electronicscale.event.ElectronicScaleWeightEvent;
import com.inossem.wms.bizbasis.electronicscale.service.datawrap.LogElectronicScaleRecordDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageCellService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.bizdomain.collection.entity.BizReceiptCollectionTaskHead;
import com.inossem.wms.common.model.bizdomain.collection.po.BizReceiptCollectionTaskSearchPO;
import com.inossem.wms.common.model.bizdomain.electronicscale.dto.LogElectronicScaleRecordDTO;
import com.inossem.wms.common.model.bizdomain.electronicscale.entity.LogElectronicScaleRecord;
import com.inossem.wms.common.model.bizdomain.electronicscale.po.LogElectronicScaleRecordSearchPO;
import com.inossem.wms.common.model.bizdomain.electronicscale.vo.LogElectronicScaleRecordVO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.cell.dto.DicWhStorageCellDTO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 电子秤移动记录 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-09
 */
@Service
@Slf4j
public class LogElectronicScaleRecordComponent {
    @Resource
    private ApplicationEventMulticaster applicationEventMulticaster;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    private WhStorageCellService whStorageCellService;
    @Autowired
    private LogElectronicScaleRecordDataWrap logElectronicScaleRecordDataWrap;

    /**
     * 无界队列用于装载上报的数据
     */
    private static final BlockingQueue<LogElectronicScaleRecordDTO> ELECTRONIC_SCALE_RECORD_QUEUE = Queues.newLinkedBlockingQueue();

    /**
     * 批量存入数量
     */
    private final static int NUM_ELEMENTS = 100;

    /**
     * 等待超时时间（单位:秒）
     */
    private final static int TIME_OUT = 10;

    /**
     * 电子秤移动记录-分页
     *
     * @param ctx 入参上下文 {@link LogElectronicScaleRecordSearchPO : "电子秤移动记录查询对象"}
     */
    public void getPage(BizContext ctx) {
        // 入参上下文
        LogElectronicScaleRecordSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装查询条件
        QueryWrapper<LogElectronicScaleRecord> wrapper = this.setQueryWrapper(po);

        // 分页处理
        IPage<LogElectronicScaleRecord> page = new Page<>(po.getPageIndex(), po.getPageSize());
        logElectronicScaleRecordDataWrap.page(page, wrapper);
        // 转dto
        List<LogElectronicScaleRecordVO> dtoList = UtilCollection.toList(page.getRecords(), LogElectronicScaleRecordVO.class);
        // 填充关联属性
        dataFillService.fillAttr(dtoList);

        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 电子秤移动记录-校验数据
     *
     * @param ctx 入参上下文 {@link LogElectronicScaleRecordDTO : "电子秤移动记录"}
     */
    public void checkElectronicScaleRecordReport(BizContext ctx) {
        // 入参上下文
        LogElectronicScaleRecordDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po)) {
            if (UtilString.isNullOrEmpty(po.getElectronicScaleId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            if (UtilNumber.isNull(po.getCurrentWeight())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            if (UtilNumber.isNull(po.getVariableWeight())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            if (UtilNumber.isNull(po.getCurrentQty())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            if (UtilNumber.isNull(po.getVariableQty())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            DicWhStorageCellDTO dicWhStorageCellDTO = whStorageCellService.getWhStorageCellByCode(po.getElectronicScaleId());
            if (UtilObject.isNull(dicWhStorageCellDTO)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_ELECTRONIC_SCALE_DOES_NOT_EXIST, po.getElectronicScaleId());
            } else {
                po.setMatId(dicWhStorageCellDTO.getMatId());
                po.setWeightUnit("g");
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }

        /* *** 推送MQ异步上报电子秤变更记录 *** */
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.ELECTRONIC_SCALE_RECORD_REPORT, ctx);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 发布电子秤重量变更事件
     *
     * @param ctx 入参上下文 {@link LogElectronicScaleRecordDTO : "电子秤移动记录"}
     */
    public void multicastEvent(BizContext ctx) {
        applicationEventMulticaster.multicastEvent(new ElectronicScaleWeightEvent(this, ctx));
    }

    /**
     * 电子秤移动记录-数据处理
     *
     * @param ctx 入参上下文 {@link LogElectronicScaleRecordDTO : "电子秤移动记录"}
     */
    public void electronicScaleRecordDataHandle(BizContext ctx) {
        // 入参上下文
        LogElectronicScaleRecordDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<LogElectronicScaleRecordDTO> logElectronicScaleRecordDTOList = new ArrayList<>(NUM_ELEMENTS);
        try {
            ELECTRONIC_SCALE_RECORD_QUEUE.put(po.setCreateTime(UtilDate.getNow()));
            // 先进先出,按规定数量为一组，数量不足按时间
            Queues.drain(ELECTRONIC_SCALE_RECORD_QUEUE, logElectronicScaleRecordDTOList, NUM_ELEMENTS, TIME_OUT, TimeUnit.SECONDS);
            // 批量保存电子秤移动记录
            this.multiSaveElectronicScale(logElectronicScaleRecordDTOList);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 电子秤移动记录-校验电子秤
     *
     * @param po 查询条件
     */
    public void checkElectronicScale(LogElectronicScaleRecordSearchPO po) {
        if (UtilObject.isNull(po) || UtilString.isNullOrEmpty(po.getElectronicScaleId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        /* *** 校验电子秤 *** */
        DicWhStorageCellDTO dicWhStorageCellDTO = whStorageCellService.getWhStorageCellByCode(po.getElectronicScaleId());
        if (UtilObject.isNull(dicWhStorageCellDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ELECTRONIC_SCALE_DOES_NOT_EXIST, po.getElectronicScaleId());
        }
        if (2 != dicWhStorageCellDTO.getCellType()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ELECTRONIC_SCALE_NOT_TYPE, po.getElectronicScaleId());
        }
        if (EnumRealYn.TRUE.getIntValue().equals(dicWhStorageCellDTO.getIsFreeze())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ELECTRONIC_SCALE_FROZEN, po.getElectronicScaleId());
        }
    }

    /**
     * 电子秤移动记录-电子秤最新一条记录
     *
     * @param po 查询条件
     */
    public LogElectronicScaleRecordDTO getLastOne(LogElectronicScaleRecordSearchPO po) {
        // 获取电子秤最新一条上报记录
        LogElectronicScaleRecordDTO logElectronicScaleRecordDTO = this.getOne(new QueryWrapper<LogElectronicScaleRecord>() {{
            lambda().eq(LogElectronicScaleRecord::getElectronicScaleId, po.getElectronicScaleId());
            lambda().eq(LogElectronicScaleRecord::getMatId, po.getMatId());
            lambda().orderByDesc(LogElectronicScaleRecord::getCreateTime);
            lambda().last("limit 1");
        }});

        // 校验电子秤上报数据
        if (UtilObject.isNull(logElectronicScaleRecordDTO)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ELECTRONIC_SCALE_RECORD_NOT_EXIST, po.getElectronicScaleId());
        }
        return logElectronicScaleRecordDTO;
    }

    /**
     * 批量保存电子秤移动记录
     *
     * @param logElectronicScaleRecordDTOList 电子秤移动记录集合
     */
    private void multiSaveElectronicScale(List<LogElectronicScaleRecordDTO> logElectronicScaleRecordDTOList) {
        if (UtilCollection.isNotEmpty(logElectronicScaleRecordDTOList)) {
            logElectronicScaleRecordDataWrap.saveBatchDto(logElectronicScaleRecordDTOList);
        }
    }

    /**
     * 获取单条电子秤上报记录
     *
     * @param wrapper 查询条件
     * @return LogElectronicScaleRecordDTO
     */
    private LogElectronicScaleRecordDTO getOne(QueryWrapper<LogElectronicScaleRecord> wrapper) {
        if (UtilObject.isNull(wrapper)) {
            return null;
        }

        LogElectronicScaleRecord logElectronicScaleRecord = logElectronicScaleRecordDataWrap.getOne(wrapper);
        LogElectronicScaleRecordDTO logElectronicScaleRecordDTO = null;
        if (UtilObject.isNotNull(logElectronicScaleRecord)){
            logElectronicScaleRecordDTO = UtilBean.newInstance(logElectronicScaleRecordDataWrap.getOne(wrapper), LogElectronicScaleRecordDTO.class);
        }

        return logElectronicScaleRecordDTO;
    }

    /**
     * 设置列表查询条件
     *
     * @param po 查询条件
     * @return QueryWrapper<LogElectronicScaleRecord>
     */
    public QueryWrapper<LogElectronicScaleRecord> setQueryWrapper(LogElectronicScaleRecordSearchPO po) {
        if (null == po) {
            po = new LogElectronicScaleRecordSearchPO();
        }
        Date startTime = null;
        Date endTime = null;
        if (UtilObject.isNotNull(po.getStartTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getStartTime());
        }
        if (UtilObject.isNotNull(po.getEndTime())) {
            endTime = UtilLocalDateTime.getEndTime(po.getEndTime());
        }
        // 查询条件设置
        QueryWrapper<LogElectronicScaleRecord> wrapper = new QueryWrapper<>();
        // 时间段
        wrapper.lambda().between((UtilObject.isNotNull(startTime)), LogElectronicScaleRecord::getCreateTime, startTime, endTime);
        return wrapper;
    }


}

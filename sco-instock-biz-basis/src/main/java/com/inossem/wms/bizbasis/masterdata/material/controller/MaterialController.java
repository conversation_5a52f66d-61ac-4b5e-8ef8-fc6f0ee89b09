package com.inossem.wms.bizbasis.masterdata.material.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSavePO;
import com.inossem.wms.common.model.masterdata.mat.info.po.DicMaterialSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.DicMaterialPageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 物料描述表 前端控制器
 *
 * <AUTHOR>
 * @date 2021/2/27
 */
@RestController
@Api(tags = "物料主数据管理")
public class MaterialController {

    @Autowired
    protected MaterialService materialService;

    /**
     * 获取物料列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 物料集合列表
     */
    @ApiOperation(value = "获取物料列表", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicMaterialPageVO>> getPage(@RequestBody DicMaterialSearchPO po, BizContext ctx) {
        return BaseResult.success(materialService.getPage(ctx));
    }

    /**
     * 导出物料列表
     */
    @ApiOperation(value = "获取物料列表", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody DicMaterialSearchPO po, BizContext ctx) {
        po.setPageSize(-1);
        materialService.export(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 查看物料详情
     *
     * <AUTHOR>
     * @param code 物料code
     * @return 物料详情
     */
    @ApiOperation(value = "按照物料编码查找物料", tags = {"物料主数据管理"})
    @GetMapping(path = "/master-data/material-code/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicMaterialDTO>> query(@PathVariable("code") String code, BizContext ctx) {
        return BaseResult.success(materialService.getByCode(ctx));
    }

    /**
     * 查看物料详情
     *
     * <AUTHOR>
     * @param id 物料Id
     * @return 物料详情
     */
    @ApiOperation(value = "按照物料编码查找物料", tags = {"物料主数据管理"})
    @GetMapping(path = "/master-data/material/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicMaterialDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(materialService.get(ctx));
    }

    /**
     * 新增物料
     *
     * <AUTHOR>
     * @param po 物料入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增物料信息", notes = "对物料信息进行添加", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicMaterialSavePO po, BizContext ctx) {
        // 储物料信息
        materialService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_SAVE_SUCCESS, po.getMaterialInfo().getMatCode());
    }

    /**
     * 修改物料
     *
     * <AUTHOR>
     * @param po 物料入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改物料信息", notes = "对物料信息进行修改", tags = {"物料主数据管理"})
    @PutMapping(path = "/master-data/material", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicMaterialSavePO po, BizContext ctx) {
        // 储物料信息
        materialService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_SAVE_SUCCESS, po.getMaterialInfo().getMatCode());
    }

    /**
     * 删除物料
     *
     * @param id 物料Id
     * <AUTHOR>
     * @return 删除结果
     */
    @ApiOperation(value = "按照物料编码删除物料", notes = "逻辑删除", tags = {"物料主数据管理"})
    @DeleteMapping(path = "/master-data/material/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除物料信息
        String matCode = materialService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_DELETE_SUCCESS, matCode);
    }

    /**
     * 物料同步
     * @param matCode 物料编码
     * @param ctx 系统上下文
     */
    @ApiOperation(value = "物料同步（单值）", notes = "主数据管理-物料同步（单值）matCode 物料编码", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material/synchronization", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> dicMaterialSynchronization(@RequestBody LogMaterialNetWeightRecordSearchPO po, BizContext ctx) {
        // 同步物料信息
        materialService.dicMaterialSynchronization(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_SYS_SUCCESS);
    }

    /**
     * 物料同步（期初）
     *
     * @param ctx 系统上下文
     */
    @ApiOperation(value = "物料同步（期初）", notes = "主数据管理-物料同步（期初）matCode 物料编码", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material/synchronization/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> materialSyncInit(BizContext ctx) {
        // 同步物料信息
        materialService.materialSyncInit(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 物料主数据导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "物料主数据导入", notes = "物料主数据导入", tags = {"物料主数据管理"})
    @PostMapping(path = "/master-data/material/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importMaterial(@RequestPart("file") MultipartFile file, BizContext ctx) {

        materialService.importMaterial(ctx);
        return BaseResult.success();
    }

    @GetMapping(path = "/master-data/material/synchronization/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> synAll(BizContext ctx) {
        materialService.synAll();
        return BaseResult.success();
    }

    /**
     * 获取所有计量单位
     */
    @ApiOperation(value = "获取所有计量单位", tags = {"物料主数据管理"})
    @GetMapping(path = "/master-data/getAllUnit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DicUnit>> getAllUnit() {
        return BaseResult.success(materialService.getAllUnit());
    }
}

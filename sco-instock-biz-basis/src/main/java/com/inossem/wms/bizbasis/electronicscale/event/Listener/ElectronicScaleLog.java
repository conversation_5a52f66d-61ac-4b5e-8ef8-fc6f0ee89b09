package com.inossem.wms.bizbasis.electronicscale.event.Listener;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.electronicscale.event.ElectronicScaleElectricQuantityEvent;
import com.inossem.wms.bizbasis.electronicscale.event.ElectronicScaleStatusEvent;
import com.inossem.wms.bizbasis.electronicscale.event.ElectronicScaleWeightEvent;
import com.inossem.wms.bizbasis.electronicscale.service.conmponent.LogElectronicScaleRecordComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @desc 电子秤状态日志持久化监听器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ElectronicScaleLog {

    @Autowired
    private LogElectronicScaleRecordComponent logElectronicScaleRecordComponent;

    @EventListener(classes={ElectronicScaleWeightEvent.class})
    public void persistenceWeightLog(ElectronicScaleWeightEvent event) {
        log.info(">>>>>>>重量变化持久化事件监听到电子秤重量变化，开始记录日志 data:{}", JSONObject.toJSON(event.getBizContext()));
        logElectronicScaleRecordComponent.electronicScaleRecordDataHandle(event.getBizContext());
    }

    @EventListener(classes={ElectronicScaleStatusEvent.class})
    public void persistenceStatusLog(ElectronicScaleStatusEvent event) {
        //@todo 状态变化数据持久化逻辑
    }

    @EventListener(classes={ElectronicScaleElectricQuantityEvent.class})
    public void persistenceElectricQuantityLog(ElectronicScaleElectricQuantityEvent event) {
        //@todo 电量数据变化持久化逻辑
    }

}

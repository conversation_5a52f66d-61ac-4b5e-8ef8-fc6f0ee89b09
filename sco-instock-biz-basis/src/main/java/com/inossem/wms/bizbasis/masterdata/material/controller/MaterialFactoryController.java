package com.inossem.wms.bizbasis.masterdata.material.controller;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialFactoryService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.tag.TagMapVo;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySavePO;
import com.inossem.wms.common.model.masterdata.mat.fty.po.DicMaterialFactorySearchPO;
import com.inossem.wms.common.model.masterdata.mat.fty.vo.DicMaterialFactoryPageVO;

import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

/**
 * 物料工厂
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "物料工厂管理")
public class MaterialFactoryController {

    @Autowired
    protected MaterialFactoryService materialFactoryService;

    /**
     * 获取物料工厂列表
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 物料工厂集合列表
     */
    @ApiOperation(value = "获取物料工厂列表", tags = {"物料工厂管理"})
    @PostMapping(path = "/master-data/mat-fty/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicMaterialFactoryPageVO>> getPage(@RequestBody DicMaterialFactorySearchPO po, BizContext ctx) {
        return BaseResult.success(materialFactoryService.getPage(ctx));
    }

    /**
     * 查看物料工厂详情
     *
     * <AUTHOR>
     * @param id 物料工厂Id
     * @return 物料工厂详情
     */
    @ApiOperation(value = "按照物料工厂编码查找物料工厂", tags = {"物料工厂管理"})
    @GetMapping(path = "/master-data/mat-fty/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicMaterialFactoryDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(materialFactoryService.get(ctx));
    }

    /**
     * 新增物料工厂
     *
     * <AUTHOR>
     * @param po 物料工厂入参类
     * @return 处理结果
     */
    @ApiOperation(value = "新增物料工厂信息", notes = "对物料工厂信息进行添加", tags = {"物料工厂管理"})
    @PostMapping(path = "/master-data/mat-fty", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicMaterialFactorySavePO po, BizContext ctx) {
        // 储物料工厂信息
        materialFactoryService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_FACTORY_SAVE_SUCCESS);
    }

    /**
     * 修改物料工厂
     *
     * <AUTHOR>
     * @param po 物料工厂入参类
     * @return 处理结果
     */
    @ApiOperation(value = "修改物料工厂信息", notes = "对物料工厂信息进行修改", tags = {"物料工厂管理"})
    @PutMapping(path = "/master-data/mat-fty", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicMaterialFactorySavePO po, BizContext ctx) {
        // 储物料工厂信息
        materialFactoryService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_FACTORY_SAVE_SUCCESS);
    }

    /**
     * 删除物料工厂
     *
     * @param id 物料工厂Id
     * <AUTHOR>
     * @return 删除结果
     */
    @ApiOperation(value = "按照物料工厂编码删除物料工厂", notes = "逻辑删除", tags = {"物料工厂管理"})
    @DeleteMapping(path = "/master-data/mat-fty/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        // 删除物料工厂信息
        materialFactoryService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_MATERIAL_FACTORY_DELETE_SUCCESS);
    }

    /**
     * 查询标签类型下拉
     *
     * @return 标签类型下拉框
     *
     */
    @ApiOperation(value = "查询标签类型下拉", tags = {"物料工厂管理"})
    @GetMapping(path = "/master-data/mat-fty/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<TagMapVo>> getDown() {
        return BaseResult.success(materialFactoryService.getDown());
    }

    /**
     * 物料工厂数据导入
     *
     * <AUTHOR>
     */
    @ApiOperation(value = "物料工厂数据导入", notes = "物料工厂数据导入", tags = {"物料工厂管理"})
    @PostMapping(path = "/master-data/mat-fty/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importMaterialFactory(@RequestPart("file") MultipartFile file, BizContext ctx) {

        materialFactoryService.importMaterialFactory(ctx);
        return BaseResult.success();
    }
}

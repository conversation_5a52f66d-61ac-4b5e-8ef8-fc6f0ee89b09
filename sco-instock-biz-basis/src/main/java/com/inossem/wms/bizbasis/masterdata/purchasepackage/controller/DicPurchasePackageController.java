package com.inossem.wms.bizbasis.masterdata.purchasepackage.controller;

import com.inossem.wms.bizbasis.masterdata.purchasepackage.service.biz.DicPurchasePackageService;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.masterdata.purchasepackage.dto.DicPurchasePackageDTO;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSavePO;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSearchPO;
import com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
/**
* 采购包管理 Api 接口
*
* <AUTHOR>
* @since 2024-07-16
*/

@Slf4j
@ApiModel(value = "DicPurchasePackageController", description = "采购包管理")
@RestController
public class DicPurchasePackageController {

    @Autowired
    private DicPurchasePackageService dicPurchasePackageService;

    /**
     * 获取采购包管理列表
     *
     * @param po 入参查询对象
     * @return 采购包管理集合列表
     */
    @ApiOperation(value = "分页获取采购包管理列表", tags = {"采购包管理"})
    @PostMapping("/dicPurchasePackage/results")
    public BaseResult<PageObjectVO<DicPurchasePackagePageVO>> getPage(@RequestBody DicPurchasePackageSearchPO po, BizContext ctx) {
        return BaseResult.success(dicPurchasePackageService.getPage(ctx));
    }


    /**
     * 获取采购包管理列表
     * @return 采购包管理集合列表
     */
    @ApiOperation(value = "获取采购包管理列表", tags = {"采购包管理"})
    @PostMapping("/dicPurchasePackage/list")
    public BaseResult<MultiResultVO<DicPurchasePackagePageVO>> getList(@RequestBody DicPurchasePackageSearchPO po, BizContext ctx) {
        return BaseResult.success(dicPurchasePackageService.getList(ctx));
    }

    /**
     * 查看采购包管理详情
     */
    @ApiOperation(value = "按照采购包管理id查找采购包管理", tags = {"采购包管理"})
    @GetMapping(path = "/dicPurchasePackage/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicPurchasePackageDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(dicPurchasePackageService.get(ctx));
    }

    /**
     * 按照采购包号查找采购包管理
     */
    @ApiOperation(value = "按照采购包号查找采购包管理", tags = {"采购包管理"})
    @GetMapping(path = "/dicPurchasePackage/by-code/{purchasePackageCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<DicPurchasePackageDTO>> getByPurchasePackageCode(@PathVariable("purchasePackageCode") String purchasePackageCode, BizContext ctx) {
        return BaseResult.success(dicPurchasePackageService.getByPurchasePackageCode(purchasePackageCode));
    }

    /**
     * 新增采购包管理
    */
    @ApiOperation(value = "新增采购包管理信息", notes = "对采购包管理信息进行添加", tags = {"采购包管理"})
    @PostMapping(path = "/dicPurchasePackage", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> add(@RequestBody DicPurchasePackageSavePO po, BizContext ctx) {
        dicPurchasePackageService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 修改采购包管理
     */
    @ApiOperation(value = "修改采购包管理信息", notes = "对采购包管理信息进行修改", tags = {"采购包管理"})
    @PutMapping(path = "/dicPurchasePackage", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> update(@RequestBody DicPurchasePackageSavePO po, BizContext ctx) {
        dicPurchasePackageService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 删除采购包管理
     *
     */
    @ApiOperation(value = "按照采购包管理id删除", notes = "逻辑删除", tags = {"采购包管理"})
    @DeleteMapping(path = "/dicPurchasePackage/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        dicPurchasePackageService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

}

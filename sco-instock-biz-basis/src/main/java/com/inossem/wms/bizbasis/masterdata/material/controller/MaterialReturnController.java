package com.inossem.wms.bizbasis.masterdata.material.controller;

import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialReturnService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.BizMaterialReturnHeadDTO;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.BizMaterialReturnHeadVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> wang
 * <p>
 *     物资返运 Controller
 * </p>
 * @date 2022/4/25 19:13
 */
@RestController
@Api(tags = "物资返运管理")
public class MaterialReturnController {

    @Autowired
    private MaterialReturnService materialReturnService;

    /**
     * 物资返运-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "物资返运-初始化", tags = {"物资返运管理"})
    @PostMapping(value = "/material-return/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInputHeadDTO>> init(BizContext ctx) {
        materialReturnService.init(ctx);
        BizResultVO<BizReceiptInputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取物资返运列表
     * @param po 入参查询对象
     * @param ctx 上下文对象
     * @return 单位关系集合列表
     */
    @ApiOperation(value = "获取物资返运列表", tags = {"物资返运管理"})
    @PostMapping(path = "/material-return/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizMaterialReturnHeadVO>> getPage(@RequestBody BizMaterialReturnSearchPO po, BizContext ctx) {
        materialReturnService.getPage(ctx);
        PageObjectVO<BizMaterialReturnHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 获取物资返-详情
     * @param id 物料Id
     * @return 物料详情
     */
    @ApiOperation(value = "物资返运详情", tags = {"物资返运管理"})
    @GetMapping(path = "/material-return/details/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialReturnHeadDTO>> query(@PathVariable("id") Long id, BizContext ctx) {
        materialReturnService.get(ctx);
        BizResultVO<BizMaterialReturnHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 新增物资返运
     * @param po
     * @return 处理结果
     */
    @ApiOperation(value = "新增物资返运", tags = {"物资返运管理"})
    @PostMapping(path = "/material-return/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody BizMaterialReturnHeadDTO po, BizContext ctx) {
        // 新增物资返运
        materialReturnService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_SAVE_SUCCESS, code);
    }


    /**
     * 物资返运-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文{"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "物资返运-删除", tags = {"入库管理-物资返运管理"})
    @DeleteMapping("/material-return/ids")
    public BaseResult<String> delete(@RequestBody BizReceiptInputDeletePO po, BizContext ctx) {
        materialReturnService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_DELETE_SUCCESS);
    }


    /**
     * 提交物资返运
     * @param po
     * @return 处理结果
     */
    @ApiOperation(value = "提交物资返运", tags = {"物资返运管理"})
    @PostMapping(path = "/material-return/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> submit(@RequestBody BizMaterialReturnHeadDTO po, BizContext ctx) {
        // 新增物资返运
        materialReturnService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.MATERIAL_RETURN_CODE_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "物资返运打印", tags = {"物资返运管理"})
    @GetMapping(path = "/material-return/print/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizMaterialReturnHeadDTO>> getInfoPrint(@PathVariable("id") Long id, BizContext ctx) {
        materialReturnService.getInfoPrint(ctx);
        BizResultVO<BizMaterialReturnHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
}

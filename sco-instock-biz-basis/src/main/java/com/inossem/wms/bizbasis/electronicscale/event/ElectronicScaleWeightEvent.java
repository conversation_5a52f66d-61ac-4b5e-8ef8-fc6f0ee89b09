package com.inossem.wms.bizbasis.electronicscale.event;

import com.inossem.wms.common.model.common.base.BizContext;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @desc 电子秤重量变化事件源
 *
 * <AUTHOR>
 */
@Getter
public class ElectronicScaleWeightEvent extends ApplicationEvent {

    private BizContext bizContext;

    public ElectronicScaleWeightEvent(Object source) {
        super(source);
    }

    public ElectronicScaleWeightEvent(Object source,BizContext bizContext) {
        super(source);
        this.bizContext = bizContext;
    }
}

package com.inossem.wms.bizbasis.masterdata.material.controller;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.common.enums.spec.SpecClassifyMapVo;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecClassifyDTO;
import com.inossem.wms.common.model.masterdata.spec.dto.BizSpecFeatureDTO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecClassifySavePO;
import com.inossem.wms.common.model.masterdata.spec.po.BizSpecClassifySearchPO;
import com.inossem.wms.common.model.masterdata.spec.vo.BizSpecClassifyPageVO;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.SpecClassifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 分类表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-11
 */
@RestController
@Slf4j
@Api(tags = "特性分类管理")
public class SpecClassifyController {

    @Autowired
    protected SpecClassifyService specClassifyService;

    /**
     * 查询分类列表
     *
     * @param po 查询条件
     * @return 分类列表
     *
     */
    @ApiOperation(value = "查询分类列表", tags = {"特性分类管理"})
    @PostMapping(path = "/master-data/spec-classify/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizSpecClassifyPageVO>> getPage(@RequestBody BizSpecClassifySearchPO po, BizContext ctx) {
        return BaseResult.success(specClassifyService.getPage(ctx));
    }

    /**
     * 查看分类详情
     *
     * @param id 主键id
     * @return 分类详情
     *
     */
    @ApiOperation(value = "查看分类详情", tags = {"特性分类管理"})
    @GetMapping(path = "/master-data/spec-classify/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<SingleResultVO<BizSpecClassifyDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(specClassifyService.getInfo(ctx));
    }

    /**
     * 新增或修改分类信息
     *
     * @param po 查询条件
     * @param ctx-cUser 当前登录人信息
     *
     */
    @ApiOperation(value = "新增或修改分类信息", tags = {"特性分类管理"})
    @PostMapping(path = "/master-data/spec-classify", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> saveOrUpdate(@RequestBody BizSpecClassifySavePO po, BizContext ctx) {
        specClassifyService.addOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SPEC_CLASSIFY_SAVE_SUCCESS, po.getBizSpecClassifyInfo().getSpecClassifyCode());
    }

    /**
     * 删除分类信息
     *
     * @param id 主键Id
     *
     */
    @ApiOperation(value = "删除分类信息", tags = {"特性分类管理"})
    @DeleteMapping(path = "/master-data/spec-classify/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        String classifyCode = specClassifyService.remove(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SPEC_FEATURE_DELETE_SUCCESS, classifyCode);
    }

    /**
     * 查询分类类型下拉
     *
     * @return 分类类型下拉框
     *
     */
    @ApiOperation(value = "查询分类类型下拉", tags = {"特性分类管理"})
    @GetMapping(path = "/master-data/spec-classify/down", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecClassifyMapVo>> getDown() {
        return BaseResult.success(specClassifyService.getDown());
    }

    /**
     * 查询特性列表
     *
     * @return 特性列表
     *
     */
    @ApiOperation(value = "查询特性列表", tags = {"特性分类管理"})
    @GetMapping(path = "/master-data/spec-classify/spec-features/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizSpecFeatureDTO>> getFeaturesList() {
        return BaseResult.success(specClassifyService.getFeaturesList());
    }
}

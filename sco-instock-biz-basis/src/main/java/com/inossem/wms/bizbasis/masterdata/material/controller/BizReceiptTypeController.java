package com.inossem.wms.bizbasis.masterdata.material.controller;


import com.inossem.wms.bizbasis.masterdata.material.service.biz.BizReceiptTypeService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.ReceiptTypeMapVO;
import com.inossem.wms.common.model.masterdata.receipt.dto.BizReceiptTypeDTO;
import com.inossem.wms.common.model.masterdata.receipt.po.BizReceiptSearchPO;
import com.inossem.wms.common.model.masterdata.receipt.po.BizReceiptTypeSavePO;
import com.inossem.wms.common.model.masterdata.receipt.vo.BizReceiptPageVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 单据类型对应存储区表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@RestController
public class BizReceiptTypeController {

    @Autowired
    private BizReceiptTypeService bizReceiptTypeService;

    /**
     * 获取单据类型对应存储区列表-分页
     *
     * @param po 入参查询对象
     * @param ctx 上下文对象
     */
    @ApiOperation(value = "获取单据类型对应存储区列表-分页", tags = {"单据类型对应存储区管理"})
    @PostMapping(path = "/receipt-type/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptPageVO>> getPage(@RequestBody BizReceiptSearchPO po, BizContext ctx) {
        return BaseResult.success(bizReceiptTypeService.getPage(ctx));
    }


    /**
     * 获取单据类型对应存储区列表-详情
     *  @param id
     * @param ctx 入参上下文 {"id":"获取单据类型对应存储区列表主键"}
     * @return
     */
    @ApiOperation(value = "单据类型对应存储区管理-详情", tags = {"单据类型对应存储区管理"})
    @GetMapping(value = "/receipt-type/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptTypeDTO> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        bizReceiptTypeService.getInfo(ctx);
        BizReceiptTypeDTO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取枚举类所有的单据类型
     */
    @ApiOperation(value = "获取所有的单据类型",tags = "单据类型对应存储区管理")
    @PostMapping(path = "/receipt-type/list")
    public BaseResult<MultiResultVO<ReceiptTypeMapVO>> getReceiptTypeList(BizContext ctx){
        return BaseResult.success(bizReceiptTypeService.getReceiptTypeList());
    }
       /* +++++++++++++++++++++++++++  未自测试*/

    /**
     * 新增、更新 单据类型对应存储区主数据
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "新增、更新 单据类型对应存储区主数据",tags = "单据类型对应存储区管理")
    @PostMapping(path = "/receipt-type/save")
    public BaseResult<?> save(@RequestBody BizReceiptTypeSavePO po, BizContext ctx){
        bizReceiptTypeService.saveOrUpdate(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 逻辑删除单据类型对应存储区主数据-批量
     * @param poList
     * @return
     */
    @ApiOperation(value = "删除单据类型对应存储区主数据-批量",tags = "单据类型对应存储区管理")
    @DeleteMapping(path = "/receipt-type/ids")
    public BaseResult<String> delete(@RequestBody List<BizReceiptTypeSavePO> poList,BizContext ctx){
        bizReceiptTypeService.deleteLogical(poList,ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
}


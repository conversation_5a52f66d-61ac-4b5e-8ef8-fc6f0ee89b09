package com.inossem.wms.bizbasis.masterdata.material.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.masterdata.material.dao.BizMaterialReturnHeadMapper;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.model.masterdata.mat.info.entity.BizMaterialReturnHead;
import com.inossem.wms.common.model.masterdata.mat.info.po.BizMaterialReturnSearchPO;
import com.inossem.wms.common.model.masterdata.mat.info.vo.BizMaterialReturnHeadVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 物资返运单抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Service
public class BizMaterialReturnHeadDataWrap extends BaseDataWrap<BizMaterialReturnHeadMapper, BizMaterialReturnHead> {


    @Autowired
    private DataFillService dataFillService;
    /**
     * 获取物资返运-列表
     * @param pageData
     * @param pageWrapper
     * @param dataFillType
     */
    public IPage<BizMaterialReturnHeadVO> getDicMaterialReturnPageVOList(IPage<BizMaterialReturnHeadVO> pageData,
                                                                         WmsQueryWrapper<BizMaterialReturnSearchPO> pageWrapper, EnumDataFillType dataFillType) {
        List<BizMaterialReturnHeadVO> inputList = this.baseMapper.getMaterialReturnList(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }
    /**
     * 获取物资返运-列表
     * @param pageData
     * @param pageWrapper
     * @param dataFillType
     */
    public IPage<BizMaterialReturnHeadVO> getDicMaterialReturnPageVOListUnitized(IPage<BizMaterialReturnHeadVO> pageData,
                                                                         WmsQueryWrapper<BizMaterialReturnSearchPO> pageWrapper, EnumDataFillType dataFillType) {
        List<BizMaterialReturnHeadVO> inputList = this.baseMapper.getMaterialReturnListUnitized(pageData, pageWrapper);
        dataFillService.fillType(dataFillType, inputList);
        return pageData.setRecords(inputList);
    }
}

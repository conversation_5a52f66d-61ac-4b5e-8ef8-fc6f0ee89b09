<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.purchasepackage.dao.DicPurchasePackageMapper">
    <!-- dic_purchase_package 采购包管理-->
    <select id="selectPageVoList"
            resultType="com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO">
        select
            id,
            purchase_package_code,
            purchase_package_name,
            contract_code,
            contract_name,
            purchase_person_name,
            is_delete,
            create_time,
            modify_time,
            create_user_id,
            modify_user_id
        from dic_purchase_package
        where dic_purchase_package.is_delete = 0
        <if test="po.purchasePackageCode != null and po.purchasePackageCode != ''">
            and dic_purchase_package.purchase_package_code = #{po.purchasePackageCode}
        </if>
        <if test="po.purchasePackageName != null and po.purchasePackageName != ''">
            and dic_purchase_package.purchase_package_name like concat( '%',#{po.purchasePackageName}, '%')
        </if>
        <if test="po.contractCode != null and po.contractCode != ''">
            and dic_purchase_package.contract_code like concat( '%',#{po.contractCode}, '%')
        </if>
        <if test="po.contractName != null and po.contractName != ''">
            and dic_purchase_package.contract_name like concat( '%',#{po.contractName}, '%')
        </if>
        <if test="po.purchasePersonName != null and po.purchasePersonName != ''">
            and dic_purchase_package.purchase_person_name like concat( '%',#{po.purchasePersonName}, '%')
        </if>
        <choose>
            <when test="po.descSortColumn != null and po.descSortColumn != ''">
                order by #{po.descSortColumn} desc
            </when>
            <when test="po.ascSortColumn != null and po.ascSortColumn != ''">
                order by #{po.ascSortColumn}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>
</mapper>

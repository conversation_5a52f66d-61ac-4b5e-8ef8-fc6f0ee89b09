package com.inossem.wms.bizbasis.masterdata.purchasepackage.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.masterdata.purchasepackage.entity.DicPurchasePackage;
import com.inossem.wms.common.model.masterdata.purchasepackage.po.DicPurchasePackageSearchPO;
import com.inossem.wms.common.model.masterdata.purchasepackage.vo.DicPurchasePackagePageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 采购包管理 mapstruct 接口
 *
 * <AUTHOR>
 * @since 2024-07-16
 * @see <a href="https://mapstruct.org/">mapstruct</a>
 */
@Mapper
public interface DicPurchasePackageMapper extends WmsBaseMapper<DicPurchasePackage> {

    /**
     * 分页查询
     */
    List<DicPurchasePackagePageVO> selectPageVoList(IPage<DicPurchasePackagePageVO> page,@Param("po") DicPurchasePackageSearchPO po);

    /**
     * 查询列表
     */
    List<DicPurchasePackagePageVO> selectPageVoList(@Param("po") DicPurchasePackageSearchPO po);
}

package com.inossem.wms.bizbasis.masterdata.costcenter.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.costcenter.service.biz.DicCostCenterService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.costcenter.entity.DicCostCenter;
import com.inossem.wms.common.model.masterdata.costcenter.po.DicCostCenterSearchPO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 成本中心管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@RestController
@Api(tags = "成本中心")
public class DicCostCenterController {

    @Autowired
    protected DicCostCenterService dicCostCenterService;

    @ApiOperation(value = "分页查询", tags = {"成本中心"})
    @PostMapping(value = "/costCenter/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicCostCenter>> getPage(@RequestBody DicCostCenterSearchPO po, BizContext ctx) {
        dicCostCenterService.getPage(ctx);
        PageObjectVO<DicCostCenter> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "同步成本中心", tags = {"成本中心"})
    @PostMapping(value = "/costCenter/sync", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> sync(BizContext ctx) {
        dicCostCenterService.sync(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
} 
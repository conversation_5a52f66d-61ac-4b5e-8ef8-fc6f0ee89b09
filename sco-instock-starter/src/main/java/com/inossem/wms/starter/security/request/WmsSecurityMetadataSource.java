package com.inossem.wms.starter.security.request;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;

import com.inossem.wms.bizbasis.masterdata.user.service.biz.ResourcesService;

/**
 * 请求时权限验证处理 1. WmsSecurityMetadataSource 对菜单ID进行数据收集 2. WmsAccessDecisionManager currentUser中的可用菜单集合进行比较,校验是否有权限
 *
 * <AUTHOR>
 * @date 2021/2/27 16:17
 */
public class WmsSecurityMetadataSource implements FilterInvocationSecurityMetadataSource {

    /**
     * 访问的资源需要那些菜单ID
     *
     * @param o 需要验证的资源
     * @return java.util.Collection<org.springframework.security.access.ConfigAttribute>
     * @date 2021/2/27 16:18
     * <AUTHOR>
     */
    @Override
    public Collection<ConfigAttribute> getAttributes(Object o) throws IllegalArgumentException {
        List<ConfigAttribute> result = new ArrayList<>();
        // 访问的Url
        String url = ((FilterInvocation)o).getRequestUrl();

        ResourcesService.SysResourcesPermission.getMenuPermission().forEach(e -> {
            // 获取所有 访问此路径 需要的菜单id
            if (url.startsWith(e.getResourcesJavaPath())) {
                ConfigAttribute attribute = new SecurityConfig(e.getId().toString());
                result.add(attribute);
            }
        });
        return new ArrayList<>(result);
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return false;
    }

}

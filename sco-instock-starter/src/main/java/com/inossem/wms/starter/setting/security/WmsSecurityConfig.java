package com.inossem.wms.starter.setting.security;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.starter.security.login.WmsBCryptPasswordEncoder;
import com.inossem.wms.starter.security.login.WmsLogoutHandler;
import com.inossem.wms.starter.security.login.WmsUserDetailsServiceImpl;
import com.inossem.wms.starter.security.login.WmsUsernamePasswordAuthenticationFilter;
import com.inossem.wms.starter.security.request.WmsAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2021/2/27 11:06 security配置
 */

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WmsSecurityConfig extends WebSecurityConfigurerAdapter {
    @Autowired
    @Lazy
    private WmsUsernamePasswordAuthenticationFilter wmsUsernamePasswordAuthenticationFilter;
    /**
     * 密码管理工具类
     */
    @Autowired
    private WmsBCryptPasswordEncoder wmsBCryptPasswordEncoder;
    /**
     * 用户服务类
     */
    @Autowired
    private WmsUserDetailsServiceImpl wmsUserDetailsServiceImpl;

    @Autowired
    private WmsLogoutHandler wmsLogoutHandler;

    /**
     * security配置
     *
     * @param http http请求
     * @date 2021/3/1 16:53
     * <AUTHOR>
     */
    @Override
    protected void configure(HttpSecurity http) {
        try {
            http.exceptionHandling()
                // token验证失败时处理
                .authenticationEntryPoint(new WmsAuthenticationEntryPointConfig())
                // 菜单权限验证失败时处理
                .accessDeniedHandler(new WmsAccessDeniedConfig()).and()
                // 配置请求时需要进行哪些资源验证
                .authorizeRequests().withObjectPostProcessor(new WmsPostProcessorConfig()).anyRequest().authenticated().and()
                // 禁用csrf
                .csrf().disable()
                // 退出处理
                .logout().logoutUrl("/logout").addLogoutHandler(wmsLogoutHandler).and()
                // 启用跨域
                .cors().and()
                // 后置过滤器
                .addFilter(wmsUsernamePasswordAuthenticationFilter).addFilter(new WmsAuthenticationFilter(authenticationManager())).httpBasic();
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SYSTEM_CONFIG_ERROR);
        }
    }

    /**
     * 不需要权限路径
     *
     * @date 2021/3/1 16:54
     * <AUTHOR>
     */
    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers(
                "/index**",
                "/swagger-ui.html/**",
                "/doc.html/**",
                "/webjars/**",
                "/favicon.ico",
                "/swagger-resources/**",
                "/v2/api-docs/**",
                "/meta_data/**",
                "/stock/modify",
                "/modeler/**",
                "/auth/token",
                "/biz-image/img/{type}/{id}",
                "/electronic-scale-record/report",
                "/master-data/cell/push-electric-status",
                "/master-data/cell/push-electric-quantity",
                "/file/**",
                "/sap/**",
                "/oilSys/**",
                "/export/**",
                "/webService/**",
                "/websocket/**",
                "/electronic-scale-record/**",
                "/3D/**",
                "/output/init/import",
                "/openapi/**",
                "/sso/createToken",
                "/sso/checkLoginToken",
                "/UMS/login.sso",
                "/init-all-cache",
                "/ihn/login/{code}",
                "/ihn/getSign",
                "/oa/login",
            "/oa/h5/login",
                "/srm/**"
        );
    }

    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() {
        try {
            return super.authenticationManagerBean();
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_SYSTEM_CONFIG_ERROR);
        }
    }

    @Bean
    public AuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setUserDetailsService(wmsUserDetailsServiceImpl);
        daoAuthenticationProvider.setPasswordEncoder(wmsBCryptPasswordEncoder);
        // security 默认忽略用户未发现异常
        daoAuthenticationProvider.setHideUserNotFoundExceptions(false);
        return daoAuthenticationProvider;
    }

}

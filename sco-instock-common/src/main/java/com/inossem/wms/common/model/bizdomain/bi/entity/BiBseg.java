package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务数据表实体类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BiBseg对象", description = "财务数据表")
@TableName("bi_bseg")
public class BiBseg implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "公司代码")
    private String bukrs;

    @ApiModelProperty(value = "会计凭证号码")
    private String belnr;

    @ApiModelProperty(value = "财年")
    private Integer gjahr;

    @ApiModelProperty(value = "会计凭证中的行项目数")
    private Integer buzei;

    @ApiModelProperty(value = "借方/贷方标识")
    private String shkzg;

    @ApiModelProperty(value = "按本位币计的金额")
    private BigDecimal dmbtr;

    @ApiModelProperty(value = "分配编号")
    private String zuonr;

    @ApiModelProperty(value = "项目文本")
    private String sgtxt;

    @ApiModelProperty(value = "总账科目")
    private String hkont;

    @ApiModelProperty(value = "客户编号")
    private String kunnr;

    @ApiModelProperty(value = "供应商或债权人的帐号")
    private String lifnr;

    @ApiModelProperty(value = "贸易伙伴的公司标识")
    private String vbund;

    @ApiModelProperty(value = "到期日期计算的起算日期")
    private Date zfbdt;

    @ApiModelProperty(value = "行项目的参考码")
    private String xref3;

    @ApiModelProperty(value = "过账码")
    private String bschl;

    @ApiModelProperty(value = "帐户类型")
    private String koart;

    @ApiModelProperty(value = "凭证货币金额")
    private BigDecimal wrbtr;

    @ApiModelProperty(value = "更新总分类帐交易数字货币")
    private String pswsl;

    @ApiModelProperty(value = "起息日")
    private Date valut;

    @ApiModelProperty(value = "总帐事务类型")
    private String vorgn;

    @ApiModelProperty(value = "控制范围")
    private String kokrs;

    @ApiModelProperty(value = "成本中心")
    private String kostl;

    @ApiModelProperty(value = "订单号")
    private String aufnr;

    @ApiModelProperty(value = "主资产号")
    private String anln1;

    @ApiModelProperty(value = "资产子编号")
    private String anln2;

    @ApiModelProperty(value = "资产交易类型")
    private String anbwa;

    @ApiModelProperty(value = "资产价值日")
    private Date bzdat;

    @ApiModelProperty(value = "物料编号")
    private String matnr;

    @ApiModelProperty(value = "工厂")
    private String werks;

    @ApiModelProperty(value = "数量")
    private BigDecimal menge;

    @ApiModelProperty(value = "基本计量单位")
    private String meins;

    @ApiModelProperty(value = "利润中心")
    private String prctr;

    @ApiModelProperty(value = "科目分配的网络号")
    private String nplnr;

    @ApiModelProperty(value = "工作分解结构元素 (wbs 元素)")
    private String projk;

    @ApiModelProperty(value = "获利能力段编号(co-pa)")
    private Integer paobjnr;

    @ApiModelProperty(value = "业务伙伴参考码")
    private String xref1;

    @ApiModelProperty(value = "业务伙伴参考码")
    private String xref2;

    @ApiModelProperty(value = "标识: 反记帐")
    private String xnegp;

    @ApiModelProperty(value = "付款参考")
    private String kidno;

    @ApiModelProperty(value = "功能范围")
    private String fkber;

    @ApiModelProperty(value = "参考过程")
    private String awtyp;

    @ApiModelProperty(value = "对象键值")
    private String awkey;

    @ApiModelProperty(value = "凭证中的过账日期")
    private Date hBudat;

    @ApiModelProperty(value = "货币码")
    private String hWaers;

    @ApiModelProperty(value = "凭证类型")
    private String hBlart;

    @ApiModelProperty(value = "物料组")
    private String matkl;

    @ApiModelProperty(value = "物料组描述")
    private String wgbez;

    @ApiModelProperty(value = "物料组描述_英文")
    private String wgbezEn;

    @ApiModelProperty(value = "日历年(过账日期年份)")
    private String calyear;

    @ApiModelProperty(value = "会计期间")
    private String fiscper3;

    @ApiModelProperty(value = "更新日期")
    private Date updDate;

    @ApiModelProperty(value = "年月(过账日期月份)")
    private String calmonth;

    @ApiModelProperty(value = "物料描述")
    private String maktx;

    @ApiModelProperty(value = "物料描述_英文")
    private String maktxEn;

    @ApiModelProperty(value = "月份(过账日期年月)")
    private String calmonth2;

    @ApiModelProperty(value = "会计年度/期间")
    private String fiscper;

    @ApiModelProperty(value = "记录建立日期")
    private Date cpudt;

    @ApiModelProperty(value = "记录建立日期")
    private Date createdon;

    @ApiModelProperty(value = "凭证日期")
    private Date bldat;

    @ApiModelProperty(value = "凭证中的过账日期")
    private Date pstngDate;

    @ApiModelProperty(value = "清帐日期")
    private Date augdt;

    @ApiModelProperty(value = "清算凭证的会计年")
    private Integer auggj;

    @ApiModelProperty(value = "凭证中的过账日期")
    private Date budat;

    @ApiModelProperty(value = "本币")
    private String hwaer;

    @ApiModelProperty(value = "财年变式")
    private String fiscvarnt;

    @ApiModelProperty(value = "分类帐")
    private String rldnr;

    @ApiModelProperty(value = "记录类型")
    private String rrcty;

    @ApiModelProperty(value = "版本")
    private String rvers;

    @ApiModelProperty(value = "财务凭证")
    private String acDocNo;

    @ApiModelProperty(value = "财务凭证-行项目")
    private String acItemNum;

    @ApiModelProperty(value = "科目表")
    private String ktopl;

    @ApiModelProperty(value = "凭证货币")
    private String waers;

    @ApiModelProperty(value = "recordmode")
    private String recordmode;

    @ApiModelProperty(value = "物料(11系统)")
    private String zmaterial;

    @ApiModelProperty(value = "物料组(11系统)")
    private String zmatgroup;

    @ApiModelProperty(value = "上次根据事务修改凭证的日期（修改）")
    private Date aedat;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;
}

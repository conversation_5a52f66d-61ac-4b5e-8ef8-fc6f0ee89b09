package com.inossem.wms.common.util;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import com.google.gson.JsonElement;
import org.springframework.lang.Nullable;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.common.constant.Const;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class UtilString {

    /**
     * 字符串是否为空
     *
     * @param value 原字符串
     * @return 真为空，假为不空
     */
    public static boolean isNullOrEmpty(String value) {

        return value == null || "null".equalsIgnoreCase(value.trim()) || Const.STRING_EMPTY.equals(value.trim());
    }

    /**
     * 字符串是否不为空
     *
     * @param value 原字符串
     * @return 假为空，真为不空
     */
    public static boolean isNotNullOrEmpty(String value) {

        return value != null && !"null".equalsIgnoreCase(value.trim()) && !Const.STRING_EMPTY.equals(value.trim());
    }

    /**
     * 字符串是否不为空
     *
     * @param value 原字符串
     * @return 真为空，假为不空
     */
    public static boolean hasText(String value) {

        return !UtilString.isNullOrEmpty(value);
    }

    /**
     * 字符串转换成int或负数
     *
     * @param value 原字符串
     * @return 目标int
     */
    public static int getIntOrNegative(String value) {
        if (isNullOrEmpty(value)) {
            return -1;
        } else {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return -1;
            }
        }
    }

    /**
     * 字符串转换成int或0
     *
     * @param value 原字符串
     * @return 目标int
     */
    public static int getIntOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0;
        } else {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 字符串转换成Integer或null
     *
     * @param value 原字符串
     * @return 目标Integer
     */
    public static Integer getIntegerOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成double或0
     *
     * @param value 原字符串
     * @return 目标double
     */
    public static double getDoubleOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0;
        } else {
            try {
                return Double.parseDouble(value);
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 字符串转换成Double
     *
     * @param value 原字符串
     * @return 目标Double
     */
    public static Double getDoubleOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Double.parseDouble(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成Float或0
     *
     * @param value 原字符串
     * @return 目标float
     */
    public static float getFloatOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0f;
        } else {
            try {
                return Float.parseFloat(value);
            } catch (Exception e) {
                return 0f;
            }
        }
    }

    /**
     * 字符串转换成Float
     *
     * @param value 原字符串
     * @return 目标Float
     */
    public static Float getFloatOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Float.parseFloat(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成Long或0
     *
     * @param value 原字符串
     * @return 目标long
     */
    public static long getLongOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0;
        } else {
            try {
                return Long.parseLong(value);
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 字符串转换成Long
     *
     * @param value 原字符串
     * @return 目标Long
     */
    public static Long getLongOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Long.parseLong(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成byte[]
     *
     * @param value 原字符串
     * @return 目标byte[]
     */
    public static byte[] getByteOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return value.getBytes();
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成char[]
     *
     * @param value 原字符串
     * @return 目标char[]
     */
    public static char[] getCharArrayOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return value.toCharArray();
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成Boolean
     *
     * @param value 原字符串
     * @return 目标Boolean
     */
    @Nullable
    public static Boolean getBooleanOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Boolean.parseBoolean(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成Boolean或者false
     *
     * @param value 原字符串
     * @return 目标Boolean
     */
    public static boolean getBooleanOrFalse(String value) {
        if (isNullOrEmpty(value)) {
            return false;
        } else {
            try {
                return Boolean.parseBoolean(value);
            } catch (Exception e) {
                return false;
            }
        }
    }

    /**
     * 字符串转换成short或0
     *
     * @param value 原字符串
     * @return 目标short
     */
    public static short getShortOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0;
        } else {
            try {
                return Short.parseShort(value);
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 字符串转换成Short
     *
     * @param value 原字符串
     * @return 目标Short
     */
    public static Short getShortOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                return Short.parseShort(value);
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 字符串转换成Integer或0
     *
     * @param value 原字符串
     * @return 目标Integer
     */
    public static Integer getIntegerOrZero(String value) {
        if (isNullOrEmpty(value)) {
            return 0;
        } else {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return 0;
            }
        }
    }

    /**
     * 字符串为null转换成空字符串""
     *
     * @param value 原字符串
     * @return 目标String
     */
    public static String getStrIfNull(String value) {
        if (isNullOrEmpty(value)) {
            return Const.STRING_EMPTY;
        }
        return value;
    }

    /**
     * 字符串转换成Date或null
     *
     * @param value 原字符串
     * @return 目标Date
     */
    public static Date getDateTimeOrNull(String value, SimpleDateFormat sf) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                ZonedDateTime zdt = LocalDateTime.parse(value, Const.FORMATTER_DATETIME1).atZone(ZoneId.systemDefault());
                return Date.from(zdt.toInstant());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    public static Date getDateTime(String value, DateTimeFormatter dtf) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                LocalTime lt = LocalTime.parse(value, dtf);
                ZonedDateTime zdt = LocalDateTime.of(LocalDate.now(), lt).atZone(ZoneId.systemDefault());
                Date strtodate = Date.from(zdt.toInstant());
                return strtodate;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    /**
     * 字符串转换成Date或null
     *
     * @param value 原字符串
     * @return 目标Date
     */
    public static Date getDateOrNull(String value) {
        if (isNullOrEmpty(value)) {
            return null;
        } else {
            try {
                DateTimeFormatter dtf = null;
                ZonedDateTime zdt = null;
                if (value.indexOf('-') > 0 && value.indexOf(':') > 0) {
                    dtf = Const.FORMATTER_DATETIME1;
                    zdt = LocalDateTime.parse(value, dtf).atZone(ZoneId.systemDefault());
                } else if (value.indexOf('/') > 0 && value.indexOf(':') > 0) {
                    dtf = Const.FORMATTER_DATETIME2;
                    zdt = LocalDateTime.parse(value, dtf).atZone(ZoneId.systemDefault());
                } else if (value.indexOf('-') > 0) {
                    dtf = Const.FORMATTER_DATE1;
                    zdt = LocalDate.parse(value, dtf).atStartOfDay(ZoneId.systemDefault());
                } else if (value.indexOf('/') > 0) {
                    dtf = Const.FORMATTER_DATE3;
                    zdt = LocalDate.parse(value, dtf).atStartOfDay(ZoneId.systemDefault());
                } else if (value.indexOf(':') > 0 && value.length() == 8) {
                    dtf = Const.FORMATTER_TIME;
                    LocalTime lt = LocalTime.parse(value, dtf);
                    zdt = LocalDateTime.of(LocalDate.now(), lt).atZone(ZoneId.systemDefault());
                } else if (value.indexOf(':') > 0 && value.length() == 5) {
                    dtf = Const.FORMATTER_TIME_MINUTE;
                    LocalTime lt = LocalTime.parse(value, dtf);
                    zdt = LocalDateTime.of(LocalDate.now(), lt).atZone(ZoneId.systemDefault());
                } else {
                    dtf = Const.FORMATTER_DATE2;
                    zdt = LocalDate.parse(value, dtf).atStartOfDay(ZoneId.systemDefault());
                }
                return Date.from(zdt.toInstant());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    /**
     * 安全解析JSON中的日期字段，先检查JsonElement是否为null，再转换为Date
     *
     * @param jsonElement JSON元素，可能为null或JsonNull
     * @return Date对象或null
     * <AUTHOR>
     * @since 2025-08-01
     */
    public static Date getDateFromJsonElement(JsonElement jsonElement) {
        if (jsonElement == null || jsonElement.isJsonNull()) {
            return null;
        }

        try {
            String dateStr = jsonElement.getAsString();
            return UtilString.getDateOrNull(dateStr);
        } catch (Exception e) {
            // 如果JsonElement不是字符串类型或转换失败，返回null
            return null;
        }
    }

    /**
     * 拆分字符串
     *
     * @param value 待拆分的字符串
     * @param separator 分隔符
     * @return String[] 拆分后字符串数组
     */
    public static String[] getStringArray(String value, String separator) {

        // 空返回，0长度
        if (isNullOrEmpty(value)) {
            return new String[0];
        }

        // 拆分
        String[] splitArray = value.split(separator);

        // 拆分后去掉null和""的字符串
        String[] retArray;
        ArrayList<String> list = new ArrayList<String>();
        if (splitArray != null && splitArray.length > 0) {
            int len = splitArray.length;
            for (int i = 0; i < len; i++) {
                if (!isNullOrEmpty((splitArray[i]))) {
                    list.add(splitArray[i]);
                }
            }

            retArray = list.toArray(new String[list.size()]);
        } else {
            retArray = new String[0];
        }
        return retArray;
    }

    /**
     * 将字符串转换num位字符串，不足num位的左补0
     *
     * @param value 原字符串
     * @param num 位数
     * @return 左补0后的字符串
     */
    public static String getStringForLeftInsertZero(String value, int num) {
        StringBuilder valueBuilder = new StringBuilder(value);
        for (int i = 0; i < num; i++) {
            if (valueBuilder.length() < num) {
                valueBuilder.insert(0, "0");
            }
        }
        value = valueBuilder.toString();
        return value;
    }

    /**
     * 除去尾随零
     *
     * @param value 字符串
     * @return
     */
    public static BigDecimal getBigDecimalStripTrailingZeros(String value) {
        if (value == null) {
            return BigDecimal.ZERO;
        } else {
            return new BigDecimal(value).stripTrailingZeros();
        }
    }

    /**
     * json转换
     *
     * @param json
     * @return
     */
    public static Object getJson(String json) {
        return JSONObject.parse(json);
    }
    public static  String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }
    /**
     * 将一个驼峰型字符串转换为snake型字符串
     *
     * @param name
     * @return
     */
    public static String getSnake(String name) {
        if (UtilString.isNullOrEmpty(name)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        result.append(name.substring(0, 1).toLowerCase());
        for (int i = 1; i < name.length(); ++i) {
            String s = name.substring(i, i + 1);
            String slc = s.toLowerCase();
            if (!(s.equals(slc))) {
                result.append("_").append(slc);
            } else {
                result.append(s);
            }
        }
        return result.toString();
    }

    /**
     * 字符串数组转snake数组
     *
     * @param argArr 转换的字符数组
     * @return java.lang.String[]
     * @date 2021/3/1
     */
    public static String[] getSnakeArr(String[] argArr) {
        if (null == argArr || argArr.length == 0) {
            return null;
        }
        int length = argArr.length;
        String[] result = new String[length];
        for (int i = 0; i < length; i++) {
            result[i] = getSnake(argArr[i]);
        }
        return result;
    }
    
    /**
     * 字符串数组转snake数组
     *
     * @param argArr 转换的字符数组
     * @return java.lang.String[]
     * @date 2021/3/1
     */
    public static List<String> getSnakeList(String[] argArr) {
        if (null == argArr || argArr.length == 0) {
            return null;
        }
        int length = argArr.length;
        String[] result = new String[length];
        for (int i = 0; i < length; i++) {
            result[i] = getSnake(argArr[i]);
        }
        return Arrays.asList(result);
    }
    

    /**
     * base64解码
     *
     * @param base64Code base64字符串
     * @return 解码后的字符串
     * <AUTHOR>
     */
    public static String base64Decode(String base64Code) {
        return new String(Base64.getUrlDecoder().decode(base64Code));
    }

    /**
     * base64编码
     *
     * @param srcCode 要编码成base64的字符串
     * @return base64字符串
     * <AUTHOR>
     */
    public static String base64Encode(String srcCode) {
        return Base64.getUrlEncoder().encodeToString(srcCode.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 对于两个字符串进行异或，进行字符串内容混淆
     *
     * @param originalString 原始字符串
     * @param mixString
     * @return 混淆后的字符串
     * <AUTHOR>
     */
    public static String xorEncrypt(String originalString, String mixString) {
        byte[] b1 = originalString.getBytes();
        byte[] b2 = mixString.getBytes();
        byte[] out = new byte[b1.length];
        for (int i = 0; i < b1.length; i++) {
            out[i] = (byte)(b1[i] ^ b2[i % b2.length]);
        }
        return new String(out);
    }

    /**
     * 驼峰转下划线
     * 
     * @date 2021/3/15 11:31
     * <AUTHOR>
     * @param camel
     * @return java.lang.String
     */
    public static String camelToUnderline(String camel) {
        return isNullOrEmpty(camel) ? camel : camel.replaceAll("[A-Z]", "_$0").toLowerCase();
    }

    /**
     * 将原字符串 按拆分字符拆分 保留空字符串 String sourceStr = "x,," retunv = ["x","",""]
     * @param sourceStr 原字符串
     * @param splitChar 拆分字符
     * @return java.lang.List
     * <AUTHOR>
     */
    public static List<String> split(String sourceStr, char splitChar) {
        List<String> retunv = new ArrayList<>();
        StringBuilder inn = new StringBuilder(Const.STRING_EMPTY);
        for (char c : sourceStr.toCharArray()) {
            if (c == splitChar) {
                retunv.add(inn.toString());
                inn = new StringBuilder(Const.STRING_EMPTY);
            } else {
                inn.append(c);
            }
        }
        retunv.add(inn.toString());

        return retunv;
    }

    /**
     * 去掉字符串前置的0
     * @param str 原始字符串，如"0000600277"
     * @return 去掉前置0的字符串，如"600277"
     */
    public static String removeLeadingZeros(String str) {
        if (isNullOrEmpty(str)) {
            return str;
        }
        // 去掉前置的0，但保留至少一位数字
        String result = str.replaceFirst("^0+", "");
        return result.isEmpty() ? "0" : result;
    }

}
